# FIM 项目总览

## 项目简介

FIM (File Access Control Agent) 是一个基于eBPF和LSM hook的高性能文件访问控制系统，支持三种工作模式和YAML策略配置。

## 核心特性

- **YAML策略配置**：支持混合allow/deny规则，企业级配置管理
- **基于inode的精准匹配**：不受路径变化影响，正确处理硬链接
- **内核级拦截**：基于eBPF LSM hook，支持所有文件操作
- **递归权限检查**：自动检查最多6层父目录权限
- **读写操作区分**：file_open hook精确区分读写操作

## 项目结构

```
FIM/
├── src/
│   ├── ebpf/                   # eBPF内核程序
│   │   ├── fim_lsm.c          # LSM hook实现
│   │   └── fim_common.h       # 共享函数和决策逻辑
│   ├── userspace/             # 用户空间工具
│   │   ├── fim-ctl.c          # 守护进程和统计
│   │   ├── fim-log.c          # 日志处理程序
│   │   ├── fim-policy.c       # YAML策略管理工具
│   │   └── common.h           # 用户空间共享头文件
│   └── include/
│       └── fim_shared.h       # 内核用户空间共享定义
├── config/
│   ├── fim.conf              # 主配置文件
│   └── policy.yaml           # YAML策略配置示例
├── scripts/
│   ├── install.sh            # 安装脚本
│   ├── uninstall.sh          # 卸载脚本
│   └── fim.service           # systemd服务文件
├── tests/
│   ├── test_basic.sh         # 基础功能测试
│   ├── test_inode_based.sh   # inode功能测试
│   └── test_performance.sh   # 性能测试
├── docs/
│   ├── README.md             # 完整使用指南
│   ├── QUICKSTART.md         # 快速入门指南
│   ├── YAML_POLICY_GUIDE.md  # YAML策略配置指南
│   ├── PROJECT_SUMMARY.md    # 技术总结
│   └── OVERVIEW.md           # 项目总览（本文件）
└── Makefile                  # 构建文件
```

## 工具链

### 核心工具
- **fim-ctl**: 守护进程管理和统计信息查看
- **fim-log**: 日志处理程序，实时监控和记录文件访问事件
- **fim-policy**: YAML策略管理工具，加载、验证、管理策略文件

### 配置文件
- **fim.conf**: 主配置文件，设置默认动作、日志级别等
- **policy.yaml**: YAML策略文件，定义详细的访问控制规则

## YAML策略模式

FIM使用YAML策略文件进行配置，支持混合allow/deny规则：

- **特点**: 最灵活，支持混合allow/deny规则
- **适用**: 复杂的企业级安全需求
- **配置**: 基于YAML文件，易于管理和版本控制
- **管理**: 通过fim-policy工具进行策略管理

## 技术架构

### eBPF LSM Hooks
- **file_open**: 文件打开权限检查（区分读写）
- **inode_create**: 文件创建拦截
- **inode_unlink**: 文件删除拦截
- **inode_rename**: 文件重命名拦截
- **inode_setattr**: 文件属性修改拦截

### 核心算法
- **inode精准匹配**: 基于inode+设备号的唯一标识
- **递归权限检查**: 检查文件及最多6层父目录
- **读写操作区分**: 根据open flags精确判断操作类型
- **策略决策引擎**: 支持三种模式的统一决策逻辑

## 快速开始

### 安装
```bash
# 安装依赖
sudo apt install -y build-essential clang llvm libbpf-dev libelf-dev libyaml-dev

# 编译安装
make
sudo ./scripts/install.sh

# 启动服务
sudo systemctl start fim
```

### 使用YAML策略
```bash
# 加载策略文件
sudo fim-policy load /etc/fim/policy.yaml

# 验证策略文件
sudo fim-policy validate /etc/fim/policy.yaml

# 查看当前策略
sudo fim-policy show

# 清空策略
sudo fim-policy clear
```

### 监控
```bash
# 实时监控
sudo fim-log -v

# 查看统计
sudo fim-ctl -c stats

# 启动守护进程
sudo fim-ctl -c daemon
```

## 应用场景

- **Web服务器**: 保护配置文件，允许内容更新
- **数据库服务器**: 严格控制数据文件访问
- **开发环境**: 防止意外修改系统文件
- **容器安全**: 限制容器内应用的文件访问
- **合规审计**: 满足安全合规要求

## 系统要求

- Linux内核版本 >= 5.8
- 支持eBPF LSM
- Root权限
- 依赖库：clang, llvm, libbpf-dev, libelf-dev, libyaml-dev

## 文档导航

- **[README.md](README.md)**: 完整的使用指南和功能介绍
- **[QUICKSTART.md](QUICKSTART.md)**: 快速安装和基本使用
- **[YAML_POLICY_GUIDE.md](YAML_POLICY_GUIDE.md)**: YAML策略配置详细说明
- **[PROJECT_SUMMARY.md](PROJECT_SUMMARY.md)**: 技术架构和实现细节

## 许可证

MIT License
