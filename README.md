# FIM (File Integrity Monitoring) System

A high-performance file integrity monitoring system based on eBPF and Linux Security Modules (LSM) with YAML-based policy configuration and read/write operation separation.

## 🚀 Features

- **Real-time monitoring**: Monitor file system operations in real-time using eBPF LSM hooks
- **Read/Write separation**: Independent policies for read and write operations
- **YAML-based policies**: Flexible configuration with deny_list and allow_list support
- **Inode-based filtering**: High-performance inode-based access control with recursive parent directory checking
- **Comprehensive LSM coverage**: 9 LSM hooks covering all major file operations
- **Zero overhead**: Kernel-space filtering with minimal performance impact
- **Production ready**: Designed for enterprise environments

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        User Space                               │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ fim-blacklist-  │  │   YAML Policy   │  │   Kernel Logs   │ │
│  │      ctl        │  │  Configuration  │  │   (trace_pipe)  │ │
│  │                 │  │                 │  │                 │ │
│  │ • Load eBPF     │  │ • read_rules    │  │ • Block events  │ │
│  │ • Parse YAML    │  │ • write_rules   │  │ • Audit logs    │ │
│  │ • Manage maps   │  │ • deny/allow    │  │                 │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                       Kernel Space                              │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    eBPF LSM Programs                        │ │
│  │                                                             │ │
│  │  file_open  │ inode_create │ inode_unlink │ inode_rename   │ │
│  │  inode_mkdir│ inode_rmdir  │ inode_setattr│ inode_link     │ │
│  │  inode_symlink                                              │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                │                                 │
│  ┌─────────────────────────────▼─────────────────────────────┐   │
│  │                    BPF Maps                               │   │
│  │                                                           │   │
│  │  read_deny_map  │ read_allow_map                         │   │
│  │  write_deny_map │ write_allow_map                        │   │
│  │                                                           │   │
│  │  Key: inode (__u32) → Value: fim_rule (struct)           │   │
│  └───────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      File System                                │
│                                                                 │
│  Files, Directories, Links, Permissions, etc.                  │
└─────────────────────────────────────────────────────────────────┘
```

## 目录结构

```
fim/
├── src/
│   ├── ebpf/                 # eBPF程序源码
│   │   ├── fim_lsm.c        # LSM hook实现
│   │   └── fim_common.h     # eBPF辅助函数
│   ├── userspace/           # 用户空间程序
│   │   ├── fim-ctl.c        # 守护进程和统计
│   │   ├── fim-log.c        # 日志处理程序
│   │   ├── fim-policy.c     # YAML策略管理工具
│   │   └── common.h         # 用户空间共享头文件
│   └── include/             # 公共头文件
│       └── fim_shared.h     # 内核用户空间共享定义
├── config/                  # 配置文件
│   ├── fim.conf            # 主配置文件
│   └── policy.yaml         # YAML策略配置
├── scripts/                 # 部署和管理脚本
│   ├── install.sh          # 安装脚本
│   ├── uninstall.sh        # 卸载脚本
│   └── fim.service         # systemd服务文件
├── tests/                   # 测试用例
│   ├── test_basic.sh       # 基础功能测试
│   ├── test_performance.sh # 性能测试
│   └── test_inode_based.sh # inode功能测试
├── Makefile                # 构建文件
└── README.md               # 项目说明
```

## 编译要求

- Linux内核版本 >= 5.8 (支持eBPF LSM)
- libbpf开发库
- clang/llvm >= 10
- 内核头文件

## 快速开始

1. 编译项目：
```bash
make all
```

2. 安装系统：
```bash
sudo ./scripts/install.sh
```

3. 加载YAML策略：
```bash
# 加载策略文件
sudo fim-policy load /etc/fim/policy.yaml

# 验证策略文件
sudo fim-policy validate /etc/fim/policy.yaml

# 查看当前策略
sudo fim-policy show
```

4. 启动服务：
```bash
sudo systemctl start fim
```

## 使用指南

### YAML策略配置

FIM使用YAML策略文件进行配置，支持混合allow/deny规则：

```yaml
global:
  default_action: deny
  description: "Production server policy"

rules:
  # 允许读取系统配置
  - path: /etc/passwd
    operations: read
    action: allow
    description: "Allow reading user database"

  # 禁止修改关键文件
  - path: /etc/shadow
    operations: write,delete
    action: deny
    description: "Protect password database"

  # 允许日志操作
  - path: /var/log
    operations: all
    action: allow
    description: "Allow logging"
```

### 策略管理

```bash
# 加载策略文件
sudo fim-policy load /etc/fim/policy.yaml

# 验证策略文件
sudo fim-policy validate /etc/fim/policy.yaml

# 查看当前策略
sudo fim-policy show

# 清空策略
sudo fim-policy clear
```

### 监控和管理

```bash
# 查看统计信息
sudo fim-ctl -c stats

# 实时监控
sudo fim-log -v

# 启动守护进程
sudo fim-ctl -c daemon
```

### 权限类型

- `read` - 读取权限
- `write` - 写入权限
- `create` - 创建权限
- `delete` - 删除权限
- `all` - 所有权限

## 文档

- [项目总览](OVERVIEW.md) - 项目结构和核心特性概览
- [快速入门指南](QUICKSTART.md) - 快速安装和基本使用
- [YAML策略配置指南](YAML_POLICY_GUIDE.md) - 详细的策略配置说明
- [技术总结](PROJECT_SUMMARY.md) - 技术架构和实现细节

## 许可证

MIT License
