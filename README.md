# FIM - 文件访问控制Agent

基于eBPF和LSM hook的高性能文件访问控制系统，支持三种工作模式和YAML策略配置。

## 核心特性

- **YAML策略配置**：支持混合allow/deny规则，企业级配置管理
- **基于inode的精准匹配**：不受路径变化影响，正确处理硬链接
- **内核级拦截**：基于eBPF LSM hook，支持所有文件操作
- **递归权限检查**：自动检查最多6层父目录权限
- **读写操作区分**：file_open hook精确区分读写操作
- **实时监控**：完整的日志记录和统计信息
- **简洁工具链**：fim-ctl（守护进程）, fim-log（监控）, fim-policy（策略管理）
- 完整的用户空间管理工具集

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    用户空间 (User Space)                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   管理工具      │  │   配置文件      │  │   日志系统      │ │
│  │  (fim-ctl)      │  │ (whitelist.conf)│  │  (fim-log)      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ eBPF syscalls
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    内核空间 (Kernel Space)                   │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                eBPF LSM Hooks                           │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │ │
│  │  │file_open    │ │inode_create │ │inode_unlink/rename  │ │ │
│  │  │(读写区分)   │ │             │ │                     │ │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                eBPF Maps                               │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │ │
│  │  │inode白名单  │ │  统计信息   │ │     事件日志        │ │ │
│  │  │(inode+dev)  │ │             │ │  (含inode信息)      │ │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 目录结构

```
fim/
├── src/
│   ├── ebpf/                 # eBPF程序源码
│   │   ├── fim_lsm.c        # LSM hook实现
│   │   └── fim_common.h     # eBPF辅助函数
│   ├── userspace/           # 用户空间程序
│   │   ├── fim-ctl.c        # 守护进程和统计
│   │   ├── fim-log.c        # 日志处理程序
│   │   ├── fim-policy.c     # YAML策略管理工具
│   │   └── common.h         # 用户空间共享头文件
│   └── include/             # 公共头文件
│       └── fim_shared.h     # 内核用户空间共享定义
├── config/                  # 配置文件
│   ├── fim.conf            # 主配置文件
│   └── policy.yaml         # YAML策略配置
├── scripts/                 # 部署和管理脚本
│   ├── install.sh          # 安装脚本
│   ├── uninstall.sh        # 卸载脚本
│   └── fim.service         # systemd服务文件
├── tests/                   # 测试用例
│   ├── test_basic.sh       # 基础功能测试
│   ├── test_performance.sh # 性能测试
│   └── test_inode_based.sh # inode功能测试
├── Makefile                # 构建文件
└── README.md               # 项目说明
```

## 编译要求

- Linux内核版本 >= 5.8 (支持eBPF LSM)
- libbpf开发库
- clang/llvm >= 10
- 内核头文件

## 快速开始

1. 编译项目：
```bash
make all
```

2. 安装系统：
```bash
sudo ./scripts/install.sh
```

3. 加载YAML策略：
```bash
# 加载策略文件
sudo fim-policy load /etc/fim/policy.yaml

# 验证策略文件
sudo fim-policy validate /etc/fim/policy.yaml

# 查看当前策略
sudo fim-policy show
```

4. 启动服务：
```bash
sudo systemctl start fim
```

## 使用指南

### YAML策略配置

FIM使用YAML策略文件进行配置，支持混合allow/deny规则：

```yaml
global:
  default_action: deny
  description: "Production server policy"

rules:
  # 允许读取系统配置
  - path: /etc/passwd
    operations: read
    action: allow
    description: "Allow reading user database"

  # 禁止修改关键文件
  - path: /etc/shadow
    operations: write,delete
    action: deny
    description: "Protect password database"

  # 允许日志操作
  - path: /var/log
    operations: all
    action: allow
    description: "Allow logging"
```

### 策略管理

```bash
# 加载策略文件
sudo fim-policy load /etc/fim/policy.yaml

# 验证策略文件
sudo fim-policy validate /etc/fim/policy.yaml

# 查看当前策略
sudo fim-policy show

# 清空策略
sudo fim-policy clear
```

### 监控和管理

```bash
# 查看统计信息
sudo fim-ctl -c stats

# 实时监控
sudo fim-log -v

# 启动守护进程
sudo fim-ctl -c daemon
```

### 权限类型

- `read` - 读取权限
- `write` - 写入权限
- `create` - 创建权限
- `delete` - 删除权限
- `all` - 所有权限

## 文档

- [项目总览](OVERVIEW.md) - 项目结构和核心特性概览
- [快速入门指南](QUICKSTART.md) - 快速安装和基本使用
- [YAML策略配置指南](YAML_POLICY_GUIDE.md) - 详细的策略配置说明
- [技术总结](PROJECT_SUMMARY.md) - 技术架构和实现细节

## 许可证

MIT License
