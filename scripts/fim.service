[Unit]
Description=File Access Control Agent
Documentation=man:fim-ctl(8)
After=network.target local-fs.target
Wants=network.target

[Service]
Type=forking
ExecStart=/usr/bin/fim-ctl -c daemon
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID
PIDFile=/var/run/fim.pid
Restart=always
RestartSec=5
TimeoutStartSec=30
TimeoutStopSec=30

# 用户和组
User=root
Group=root

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log /etc/fim /var/run
PrivateTmp=true
PrivateDevices=true
ProtectHostname=true
ProtectClock=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectKernelLogs=true
ProtectControlGroups=true
RestrictAddressFamilies=AF_UNIX AF_INET AF_INET6 AF_NETLINK
RestrictNamespaces=true
LockPersonality=true
MemoryDenyWriteExecute=false
RestrictRealtime=true
RestrictSUIDSGID=true
RemoveIPC=true

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=256M
CPUQuota=50%

# 环境变量
Environment=FIM_CONFIG_DIR=/etc/fim
Environment=FIM_LOG_LEVEL=info

[Install]
WantedBy=multi-user.target
