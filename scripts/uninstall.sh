#!/bin/bash

# File Access Control Agent Uninstallation Script
# 文件访问控制代理卸载脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
INSTALL_DIR="/usr/lib/fim"
CONFIG_DIR="/etc/fim"
BIN_DIR="/usr/bin"
LOG_DIR="/var/log"
SERVICE_DIR="/etc/systemd/system"

# 检查函数
check_root() {
    if [[ $EUID -ne 0 ]]; then
        echo -e "${RED}Error: This script must be run as root${NC}"
        exit 1
    fi
}

confirm_uninstall() {
    echo -e "${YELLOW}WARNING: This will completely remove the File Access Control Agent${NC}"
    echo -e "${YELLOW}This includes all configuration files and logs!${NC}"
    echo -e "\nThe following will be removed:"
    echo -e "- Binaries: $BIN_DIR/fim-ctl, $BIN_DIR/fim-log"
    echo -e "- Installation directory: $INSTALL_DIR"
    echo -e "- Configuration directory: $CONFIG_DIR"
    echo -e "- Log files: $LOG_DIR/fim.log*"
    echo -e "- Systemd services: fim.service, fim-log.service"
    
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}Uninstallation cancelled${NC}"
        exit 0
    fi
}

stop_services() {
    echo -e "${BLUE}Stopping services...${NC}"
    
    # 停止服务
    if systemctl is-active --quiet fim.service; then
        systemctl stop fim.service
        echo -e "${GREEN}Stopped fim.service${NC}"
    fi
    
    if systemctl is-active --quiet fim-log.service; then
        systemctl stop fim-log.service
        echo -e "${GREEN}Stopped fim-log.service${NC}"
    fi
    
    # 禁用服务
    if systemctl is-enabled --quiet fim.service; then
        systemctl disable fim.service
        echo -e "${GREEN}Disabled fim.service${NC}"
    fi
    
    if systemctl is-enabled --quiet fim-log.service; then
        systemctl disable fim-log.service
        echo -e "${GREEN}Disabled fim-log.service${NC}"
    fi
}

remove_services() {
    echo -e "${BLUE}Removing systemd services...${NC}"
    
    # 删除服务文件
    if [[ -f "$SERVICE_DIR/fim.service" ]]; then
        rm -f "$SERVICE_DIR/fim.service"
        echo -e "${GREEN}Removed fim.service${NC}"
    fi
    
    if [[ -f "$SERVICE_DIR/fim-log.service" ]]; then
        rm -f "$SERVICE_DIR/fim-log.service"
        echo -e "${GREEN}Removed fim-log.service${NC}"
    fi
    
    # 重新加载systemd
    systemctl daemon-reload
    systemctl reset-failed
}

remove_binaries() {
    echo -e "${BLUE}Removing binaries...${NC}"
    
    # 删除二进制文件
    if [[ -f "$BIN_DIR/fim-ctl" ]]; then
        rm -f "$BIN_DIR/fim-ctl"
        echo -e "${GREEN}Removed fim-ctl${NC}"
    fi
    
    if [[ -f "$BIN_DIR/fim-log" ]]; then
        rm -f "$BIN_DIR/fim-log"
        echo -e "${GREEN}Removed fim-log${NC}"
    fi

    if [[ -f "$BIN_DIR/fim-inode-tool" ]]; then
        rm -f "$BIN_DIR/fim-inode-tool"
        echo -e "${GREEN}Removed fim-inode-tool${NC}"
    fi
}

remove_installation() {
    echo -e "${BLUE}Removing installation directory...${NC}"
    
    if [[ -d "$INSTALL_DIR" ]]; then
        rm -rf "$INSTALL_DIR"
        echo -e "${GREEN}Removed $INSTALL_DIR${NC}"
    fi
}

remove_configs() {
    echo -e "${BLUE}Removing configuration files...${NC}"
    
    if [[ -d "$CONFIG_DIR" ]]; then
        # 备份配置文件
        backup_dir="/tmp/fim_config_backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$backup_dir"
        cp -r "$CONFIG_DIR"/* "$backup_dir/" 2>/dev/null || true
        echo -e "${YELLOW}Configuration files backed up to: $backup_dir${NC}"
        
        # 删除配置目录
        rm -rf "$CONFIG_DIR"
        echo -e "${GREEN}Removed $CONFIG_DIR${NC}"
    fi
}

remove_logs() {
    echo -e "${BLUE}Removing log files...${NC}"
    
    # 删除日志文件
    if [[ -f "$LOG_DIR/fim.log" ]]; then
        rm -f "$LOG_DIR/fim.log"*
        echo -e "${GREEN}Removed fim log files${NC}"
    fi
    
    # 删除PID文件
    if [[ -f "/var/run/fim.pid" ]]; then
        rm -f "/var/run/fim.pid"
        echo -e "${GREEN}Removed PID file${NC}"
    fi
}

cleanup_processes() {
    echo -e "${BLUE}Cleaning up processes...${NC}"
    
    # 查找并终止相关进程
    local pids=$(pgrep -f "fim-ctl\|fim-log" 2>/dev/null || true)
    if [[ -n "$pids" ]]; then
        echo -e "${YELLOW}Found running FIM processes: $pids${NC}"
        kill -TERM $pids 2>/dev/null || true
        sleep 2
        
        # 强制终止仍在运行的进程
        pids=$(pgrep -f "fim-ctl\|fim-log" 2>/dev/null || true)
        if [[ -n "$pids" ]]; then
            kill -KILL $pids 2>/dev/null || true
            echo -e "${GREEN}Forcefully terminated remaining processes${NC}"
        fi
    fi
}

check_ebpf_programs() {
    echo -e "${BLUE}Checking for loaded eBPF programs...${NC}"
    
    # 检查是否有FIM相关的eBPF程序仍在运行
    local bpf_progs=$(bpftool prog list 2>/dev/null | grep -i fim || true)
    if [[ -n "$bpf_progs" ]]; then
        echo -e "${YELLOW}Warning: Found loaded eBPF programs that may be related to FIM:${NC}"
        echo "$bpf_progs"
        echo -e "${YELLOW}You may need to manually unload these programs or reboot the system${NC}"
    fi
}

print_summary() {
    echo -e "\n${GREEN}=== Uninstallation Summary ===${NC}"
    echo -e "${GREEN}File Access Control Agent has been successfully removed${NC}"
    echo -e "\nRemoved components:"
    echo -e "- Systemd services (fim.service, fim-log.service)"
    echo -e "- Binary files (fim-ctl, fim-log)"
    echo -e "- Installation directory ($INSTALL_DIR)"
    echo -e "- Configuration directory ($CONFIG_DIR)"
    echo -e "- Log files"
    echo -e "- Process and PID files"
    
    if [[ -d "/tmp/fim_config_backup_"* ]]; then
        echo -e "\n${YELLOW}Note: Configuration files have been backed up to /tmp/${NC}"
    fi
    
    echo -e "\n${YELLOW}Important:${NC}"
    echo -e "- Any loaded eBPF programs may still be active until system reboot"
    echo -e "- Consider rebooting the system to ensure complete cleanup"
    echo -e "- Check system logs for any remaining references"
    
    echo -e "\n${GREEN}Uninstallation completed successfully!${NC}"
}

# 主卸载流程
main() {
    echo -e "${BLUE}=== File Access Control Agent Uninstallation ===${NC}\n"
    
    check_root
    confirm_uninstall
    cleanup_processes
    stop_services
    remove_services
    remove_binaries
    remove_installation
    remove_configs
    remove_logs
    check_ebpf_programs
    print_summary
}

# 运行卸载
main "$@"
