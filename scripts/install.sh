#!/bin/bash

# File Access Control Agent Installation Script
# 文件访问控制代理安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
INSTALL_DIR="/usr/lib/fim"
CONFIG_DIR="/etc/fim"
BIN_DIR="/usr/bin"
LOG_DIR="/var/log"
SERVICE_DIR="/etc/systemd/system"

# 检查函数
check_root() {
    if [[ $EUID -ne 0 ]]; then
        echo -e "${RED}Error: This script must be run as root${NC}"
        exit 1
    fi
}

check_kernel_version() {
    local kernel_version=$(uname -r | cut -d. -f1,2)
    local major=$(echo $kernel_version | cut -d. -f1)
    local minor=$(echo $kernel_version | cut -d. -f2)
    
    if [[ $major -lt 5 ]] || [[ $major -eq 5 && $minor -lt 8 ]]; then
        echo -e "${RED}Error: Kernel version 5.8 or higher is required for eBPF LSM support${NC}"
        echo "Current kernel version: $(uname -r)"
        exit 1
    fi
    
    echo -e "${GREEN}Kernel version check passed: $(uname -r)${NC}"
}

check_dependencies() {
    local deps=("clang" "llvm-strip" "libbpf-dev")
    local missing_deps=()
    
    echo -e "${BLUE}Checking dependencies...${NC}"
    
    for dep in "${deps[@]}"; do
        if ! command -v ${dep%%-dev} &> /dev/null && ! dpkg -l | grep -q $dep; then
            missing_deps+=($dep)
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        echo -e "${YELLOW}Missing dependencies: ${missing_deps[*]}${NC}"
        echo -e "${BLUE}Installing missing dependencies...${NC}"
        
        if command -v apt-get &> /dev/null; then
            apt-get update
            apt-get install -y "${missing_deps[@]}"
        elif command -v yum &> /dev/null; then
            yum install -y "${missing_deps[@]}"
        elif command -v dnf &> /dev/null; then
            dnf install -y "${missing_deps[@]}"
        else
            echo -e "${RED}Error: Unable to install dependencies automatically${NC}"
            echo "Please install the following packages manually: ${missing_deps[*]}"
            exit 1
        fi
    fi
    
    echo -e "${GREEN}All dependencies satisfied${NC}"
}

create_directories() {
    echo -e "${BLUE}Creating directories...${NC}"
    
    mkdir -p "$INSTALL_DIR"
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$LOG_DIR"
    
    echo -e "${GREEN}Directories created${NC}"
}

install_binaries() {
    echo -e "${BLUE}Installing binaries...${NC}"
    
    # 复制eBPF程序
    if [[ -f "build/fim_lsm.o" ]]; then
        cp build/fim_lsm.o "$INSTALL_DIR/"
        chmod 644 "$INSTALL_DIR/fim_lsm.o"
    else
        echo -e "${RED}Error: eBPF program not found. Please run 'make' first.${NC}"
        exit 1
    fi
    
    # 复制用户空间程序
    if [[ -f "bin/fim-ctl" ]]; then
        cp bin/fim-ctl "$BIN_DIR/"
        chmod 755 "$BIN_DIR/fim-ctl"
    else
        echo -e "${RED}Error: fim-ctl not found. Please run 'make' first.${NC}"
        exit 1
    fi
    
    if [[ -f "bin/fim-log" ]]; then
        cp bin/fim-log "$BIN_DIR/"
        chmod 755 "$BIN_DIR/fim-log"
    else
        echo -e "${RED}Error: fim-log not found. Please run 'make' first.${NC}"
        exit 1
    fi

    if [[ -f "bin/fim-policy" ]]; then
        cp bin/fim-policy "$BIN_DIR/"
        chmod 755 "$BIN_DIR/fim-policy"
    else
        echo -e "${RED}Error: fim-policy not found. Please run 'make' first.${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}Binaries installed${NC}"
}

install_configs() {
    echo -e "${BLUE}Installing configuration files...${NC}"
    
    # 复制配置文件
    if [[ -f "config/fim.conf" ]]; then
        cp config/fim.conf "$CONFIG_DIR/"
        chmod 644 "$CONFIG_DIR/fim.conf"
    fi
    
    if [[ -f "config/whitelist.conf" ]]; then
        cp config/whitelist.conf "$CONFIG_DIR/"
        chmod 644 "$CONFIG_DIR/whitelist.conf"
    fi
    
    echo -e "${GREEN}Configuration files installed${NC}"
}

install_service() {
    echo -e "${BLUE}Installing systemd service...${NC}"
    
    # 创建systemd服务文件
    cat > "$SERVICE_DIR/fim.service" << EOF
[Unit]
Description=File Access Control Agent
After=network.target
Wants=network.target

[Service]
Type=forking
ExecStart=$BIN_DIR/fim-ctl -c daemon
ExecReload=/bin/kill -HUP \$MAINPID
PIDFile=/var/run/fim.pid
Restart=always
RestartSec=5
User=root
Group=root

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$LOG_DIR $CONFIG_DIR /var/run
PrivateTmp=true

[Install]
WantedBy=multi-user.target
EOF
    
    chmod 644 "$SERVICE_DIR/fim.service"
    
    # 创建日志服务文件
    cat > "$SERVICE_DIR/fim-log.service" << EOF
[Unit]
Description=File Access Control Agent Logger
After=fim.service
Requires=fim.service

[Service]
Type=simple
ExecStart=$BIN_DIR/fim-log -D
Restart=always
RestartSec=5
User=root
Group=root

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$LOG_DIR
PrivateTmp=true

[Install]
WantedBy=multi-user.target
EOF
    
    chmod 644 "$SERVICE_DIR/fim-log.service"
    
    # 重新加载systemd
    systemctl daemon-reload
    
    echo -e "${GREEN}Systemd services installed${NC}"
}

setup_permissions() {
    echo -e "${BLUE}Setting up permissions...${NC}"
    
    # 设置目录权限
    chown -R root:root "$INSTALL_DIR"
    chown -R root:root "$CONFIG_DIR"
    
    # 创建日志文件
    touch "$LOG_DIR/fim.log"
    chown root:root "$LOG_DIR/fim.log"
    chmod 644 "$LOG_DIR/fim.log"
    
    echo -e "${GREEN}Permissions configured${NC}"
}

enable_services() {
    echo -e "${BLUE}Enabling services...${NC}"
    
    systemctl enable fim.service
    systemctl enable fim-log.service
    
    echo -e "${GREEN}Services enabled${NC}"
}

print_summary() {
    echo -e "\n${GREEN}=== Installation Summary ===${NC}"
    echo -e "Installation directory: $INSTALL_DIR"
    echo -e "Configuration directory: $CONFIG_DIR"
    echo -e "Log file: $LOG_DIR/fim.log"
    echo -e "Binaries: $BIN_DIR/fim-ctl, $BIN_DIR/fim-log"
    echo -e "Services: fim.service, fim-log.service"
    echo -e "\n${YELLOW}Next steps:${NC}"
    echo -e "1. Review and customize the configuration: $CONFIG_DIR/fim.conf"
    echo -e "2. Configure the whitelist: $CONFIG_DIR/whitelist.conf"
    echo -e "3. Start the services:"
    echo -e "   sudo systemctl start fim"
    echo -e "   sudo systemctl start fim-log"
    echo -e "4. Check the status:"
    echo -e "   sudo systemctl status fim"
    echo -e "   sudo fim-ctl -c stats"
    echo -e "\n${GREEN}Installation completed successfully!${NC}"
}

# 主安装流程
main() {
    echo -e "${BLUE}=== File Access Control Agent Installation ===${NC}\n"
    
    check_root
    check_kernel_version
    check_dependencies
    create_directories
    install_binaries
    install_configs
    install_service
    setup_permissions
    enable_services
    print_summary
}

# 运行安装
main "$@"
