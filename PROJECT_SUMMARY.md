# FIM 项目技术总结

## 项目概述

FIM是基于eBPF和LSM hook的高性能文件访问控制系统，使用YAML策略配置，支持混合allow/deny规则。

## 核心技术架构

### 1. eBPF LSM程序
- 5个LSM hook点：file_open, inode_create, inode_unlink, inode_rename, inode_setattr
- file_open hook精确区分读写操作
- 基于inode的高效匹配算法
- 递归父目录权限检查（最多6层）

### 2. YAML策略系统
- **策略文件配置**：支持混合allow/deny规则
- **企业级管理**：易于版本控制和批量部署
- **灵活决策**：根据规则类型精确控制访问

### 3. 用户空间工具
- `fim-ctl`: 守护进程和统计信息
- `fim-log`: 日志处理，实时监控和记录
- `fim-policy`: YAML策略文件管理

## 技术特点

### 基于inode的精准匹配
- 使用inode+设备号作为文件唯一标识
- 不受路径变化影响
- 正确处理硬链接
- 高性能数值比较

### 递归权限检查
- 检查文件本身和最多6层父目录
- 支持目录级权限继承
- 优化的遍历算法

### 读写操作区分
- file_open hook根据flags精确区分读写
- 支持细粒度权限控制
- 避免file_permission的性能开销
## 应用场景

1. **企业安全防护**: 保护关键系统文件和配置
2. **合规性要求**: 满足数据保护和访问控制规范
3. **容器安全**: 限制容器内应用的文件访问
4. **开发环境**: 防止意外的文件修改和删除
5. **服务器加固**: 增强服务器的安全防护能力

## 系统要求

- Linux内核版本 >= 5.8 (eBPF LSM支持)
- Root权限
- clang >= 10
- libbpf开发库
- libyaml开发库

## 总结

FIM是一个完整的、高性能的文件访问控制系统，具有以下优势：

- **技术先进**: 基于eBPF LSM技术
- **模式灵活**: 支持三种工作模式
- **配置简单**: YAML策略文件易于管理
- **性能优异**: 内核级实现，响应迅速
- **功能完整**: 覆盖所有主要文件操作
