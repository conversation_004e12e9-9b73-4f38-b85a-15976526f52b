# FIM 快速入门指南

## 系统要求

- Linux内核版本 >= 5.8
- 支持eBPF LSM
- root权限

## 快速安装

```bash
# 1. 检查系统兼容性
uname -r  # 内核版本 >= 5.8
grep CONFIG_BPF_LSM /boot/config-$(uname -r)

# 2. 安装依赖
sudo apt update
sudo apt install -y build-essential clang llvm libbpf-dev libelf-dev libyaml-dev

# 3. 编译和安装
make
sudo ./scripts/install.sh

# 4. 启动服务
sudo systemctl start fim
sudo systemctl enable fim
```

## YAML策略配置

```bash
# 加载策略文件
sudo fim-policy load /etc/fim/policy.yaml

# 验证策略文件
sudo fim-policy validate /etc/fim/policy.yaml

# 查看当前策略
sudo fim-policy show

# 清空策略
sudo fim-policy clear
```

## 监控和管理

```bash
# 查看统计信息
sudo fim-ctl -c stats

# 实时监控
sudo fim-log -v

# 启动守护进程
sudo fim-ctl -c daemon
```

## 权限类型

- `read` - 读取权限
- `write` - 写入权限
- `create` - 创建权限
- `delete` - 删除权限
- `rename` - 重命名权限
- `chmod` - 权限修改
- `chown` - 所有者修改
- `all` - 所有权限

## 故障排除

```bash
# 检查服务状态
sudo systemctl status fim

# 验证eBPF程序
sudo bpftool prog list | grep lsm

# 查看日志
sudo journalctl -u fim -f
```
