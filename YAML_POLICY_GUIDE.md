# YAML Policy Configuration Guide

This guide explains how to configure FIM policies using the new YAML format with read/write operation separation.

## 📋 Policy Structure Overview

The FIM system uses a hierarchical YAML structure that separates read and write operations:

```yaml
# 全局配置
global:
  default_action: deny  # 默认动作：allow 或 deny
  log_level: info       # 日志级别：error, warn, info, debug
  description: "策略描述"

# 策略规则列表
rules:
  - path: /path/to/file
    operations: read,write
    action: allow
    description: "规则描述"

# 策略元数据
metadata:
  version: "1.0"
  author: "作者"
  environment: "环境"
```

### 规则字段说明

| 字段 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `path` | 字符串 | 是 | 文件或目录的绝对路径 |
| `operations` | 字符串 | 是 | 操作类型，逗号分隔 |
| `action` | 字符串 | 是 | 动作：`allow` 或 `deny` |
| `description` | 字符串 | 否 | 规则描述 |

### 支持的操作类型

- `read` - 读取操作
- `write` - 写入操作
- `create` - 创建操作
- `delete` - 删除操作
- `rename` - 重命名操作
- `chmod` - 权限修改
- `chown` - 所有者修改
- `all` - 所有操作

## 快速开始

```bash
# 1. 设置策略模式
sudo fim-ctl -c mode -m policy

# 2. 加载策略文件
sudo fim-policy load /etc/fim/policy.yaml

# 3. 启动服务
sudo systemctl start fim
```

## 策略示例

### Web服务器策略
```yaml
global:
  default_action: deny
  description: "Web服务器安全策略"

rules:
  # 允许Web服务器配置
  - path: /etc/nginx
    operations: read
    action: allow
    description: "允许读取nginx配置"

  # 允许Web内容目录
  - path: /var/www/html
    operations: read,write,create,delete
    action: allow
    description: "允许管理Web内容"

  # 禁止修改系统文件
  - path: /etc/passwd
    operations: write,delete
    action: deny
    description: "保护用户数据库"

  # 允许日志写入
  - path: /var/log
    operations: all
    action: allow
    description: "允许日志操作"
```

## 管理命令

```bash
# 加载策略文件
sudo fim-policy load /etc/fim/policy.yaml

# 验证策略文件
sudo fim-policy validate /etc/fim/policy.yaml

# 显示当前策略
sudo fim-policy show

# 清空策略
sudo fim-policy clear
```

## 最佳实践

1. **最小权限原则**：只给予必要的权限
2. **分层设计**：按功能分组规则
3. **添加描述**：为每个规则添加说明
4. **版本控制**：将策略文件纳入版本管理
5. **测试验证**：在生产环境前充分测试

## 故障排除

```bash
# 检查YAML语法
sudo fim-policy validate policy.yaml

# 验证文件路径
sudo fim-inode-tool info /path/to/file

# 查看当前模式
sudo fim-ctl -c mode

# 监控日志
sudo fim-log -v
```
