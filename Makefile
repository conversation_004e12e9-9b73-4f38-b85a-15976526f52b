# File Access Control Agent Makefile

# 编译器和工具
CC := clang
LLVM_STRIP := llvm-strip
ARCH := $(shell uname -m | sed 's/x86_64/x86/' | sed 's/aarch64/arm64/')

# 目录定义
SRC_DIR := src
EBPF_DIR := $(SRC_DIR)/ebpf
USER_DIR := $(SRC_DIR)/userspace
INCLUDE_DIR := $(SRC_DIR)/include
BUILD_DIR := build
BIN_DIR := bin

# eBPF相关标志
EBPF_CFLAGS := -O2 -g -Wall -Werror
EBPF_CFLAGS += -target bpf -D__TARGET_ARCH_$(ARCH)
EBPF_CFLAGS += -I$(INCLUDE_DIR) -I$(EBPF_DIR) -I/usr/include/$(shell uname -m)-linux-gnu

# 用户空间程序标志
USER_CFLAGS := -O2 -g -Wall -Werror
USER_CFLAGS += -I$(INCLUDE_DIR)
USER_LDFLAGS := -lbpf -lelf -lz -lyaml

# 目标文件
EBPF_TARGETS := $(BUILD_DIR)/fim_lsm.o
USER_TARGETS := $(BIN_DIR)/fim-blacklist-ctl

# 默认目标
.PHONY: all clean install uninstall

all: $(EBPF_TARGETS) $(USER_TARGETS)

# 创建目录
$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

$(BIN_DIR):
	mkdir -p $(BIN_DIR)

# 编译eBPF程序
$(BUILD_DIR)/fim_lsm.o: $(EBPF_DIR)/fim_lsm.c $(EBPF_DIR)/fim_common.h | $(BUILD_DIR)
	$(CC) $(EBPF_CFLAGS) -c $< -o $@
	$(LLVM_STRIP) -g $@

# 编译用户空间程序
$(BIN_DIR)/fim-blacklist-ctl: $(USER_DIR)/fim-blacklist-ctl.c | $(BIN_DIR)
	$(CC) $(USER_CFLAGS) $< -o $@ $(USER_LDFLAGS)

# 清理
clean:
	rm -rf $(BUILD_DIR) $(BIN_DIR)

# 安装
install: all
	sudo ./scripts/install.sh

# 卸载
uninstall:
	sudo ./scripts/uninstall.sh

# 测试
test: all
	./tests/test_basic.sh

# 性能测试
perf-test: all
	./tests/test_performance.sh

# 检查依赖
check-deps:
	@echo "Checking dependencies..."
	@which clang > /dev/null || (echo "clang not found" && exit 1)
	@which llvm-strip > /dev/null || (echo "llvm-strip not found" && exit 1)
	@pkg-config --exists libbpf || (echo "libbpf not found" && exit 1)
	@echo "All dependencies satisfied"

# 帮助信息
help:
	@echo "Available targets:"
	@echo "  all         - Build all components"
	@echo "  clean       - Clean build artifacts"
	@echo "  install     - Install the system"
	@echo "  uninstall   - Uninstall the system"
	@echo "  test        - Run basic tests"
	@echo "  perf-test   - Run performance tests"
	@echo "  check-deps  - Check build dependencies"
	@echo "  help        - Show this help message"
