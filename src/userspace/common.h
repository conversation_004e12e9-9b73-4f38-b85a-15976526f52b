#ifndef __FIM_USER_COMMON_H__
#define __FIM_USER_COMMON_H__

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <signal.h>
#include <time.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <fcntl.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>
#include <yaml.h>
#include "../include/fim_shared.h"

/* 默认配置路径 */
#define FIM_CONFIG_DIR      "/etc/fim"
#define FIM_WHITELIST_FILE  FIM_CONFIG_DIR "/whitelist.conf"
#define FIM_CONFIG_FILE     FIM_CONFIG_DIR "/fim.conf"
#define FIM_LOG_FILE        "/var/log/fim.log"
#define FIM_PID_FILE        "/var/run/fim.pid"

/* eBPF程序路径 */
#define FIM_EBPF_OBJ        "/usr/lib/fim/fim_lsm.o"

/* 日志宏 */
#define LOG_ERROR(fmt, ...) \
    fprintf(stderr, "[ERROR] %s:%d " fmt "\n", __func__, __LINE__, ##__VA_ARGS__)
#define LOG_WARN(fmt, ...) \
    fprintf(stderr, "[WARN] %s:%d " fmt "\n", __func__, __LINE__, ##__VA_ARGS__)
#define LOG_INFO(fmt, ...) \
    printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#define LOG_DEBUG(fmt, ...) \
    do { if (debug_mode) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__); } while(0)

/* 全局变量 */
extern int debug_mode;
extern int verbose_mode;

/* 函数声明 */

/* 配置管理 */
int load_config(const char *config_file, struct fim_config *config);
int save_config(const char *config_file, const struct fim_config *config);
int load_whitelist(const char *whitelist_file);
int save_whitelist(const char *whitelist_file);

/* eBPF程序管理 */
int load_ebpf_program(const char *obj_file);
int unload_ebpf_program(void);
int attach_lsm_hooks(struct bpf_object *obj);
void detach_lsm_hooks(void);

/* 白名单操作 */
int add_whitelist_entry(const char *path, __u32 operations);
int del_whitelist_entry(const char *path);
int clear_whitelist(void);
int list_whitelist(void);

/* 统计和监控 */
int get_stats(struct fim_stats *stats);
int reset_stats(void);
int monitor_events(void);

/* 工具函数 */
int create_pid_file(const char *pid_file);
int remove_pid_file(const char *pid_file);
int daemonize(void);
void signal_handler(int sig);
__u32 parse_operations(const char *ops_str);
const char *operation_to_string(__u32 op);
const char *decision_to_string(__u32 decision);
void print_usage(const char *prog_name);

/* 路径处理 */
int normalize_path(const char *input, char *output, size_t output_size);

/* 配置解析 */
struct config_item {
    char *key;
    char *value;
};

int parse_config_line(const char *line, struct config_item *item);
void free_config_item(struct config_item *item);

/* 白名单条目解析 */
struct whitelist_item {
    char path[FIM_MAX_PATH_LEN];
    char operations[64];
};

int parse_whitelist_line(const char *line, struct whitelist_item *item);

/* 时间格式化 */
void format_timestamp(__u64 timestamp, char *buf, size_t buf_size);

/* 文件操作辅助 */
int file_exists(const char *path);
int create_directory(const char *path, mode_t mode);
int read_file_content(const char *path, char **content, size_t *size);
int write_file_content(const char *path, const char *content, size_t size);

/* 进程管理 */
int is_process_running(pid_t pid);
pid_t get_pid_from_file(const char *pid_file);

/* 字符串处理 */
char *trim_whitespace(char *str);
int split_string(const char *str, char delimiter, char ***tokens, int *count);
void free_tokens(char **tokens, int count);

/* 权限检查 */
int check_root_privileges(void);
int check_file_permissions(const char *path, int mode);

/* 错误处理 */
void print_error(const char *msg);
void print_errno(const char *msg);

#endif /* __FIM_USER_COMMON_H__ */
