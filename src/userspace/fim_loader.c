#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <errno.h>
#include <string.h>
#include <sys/stat.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>

static struct bpf_object *obj = NULL;
static struct bpf_link *links[10];
static int link_count = 0;
static int whitemap_fd = -1;

static void cleanup(void) {
    printf("Cleaning up...\n");

    // 卸载所有BPF链接
    for (int i = 0; i < link_count; i++) {
        if (links[i]) {
            bpf_link__destroy(links[i]);
        }
    }

    // 关闭BPF对象
    if (obj) {
        bpf_object__close(obj);
    }

    printf("FIM protection disabled.\n");
}

static void signal_handler(int sig) {
    printf("\nReceived signal %d, shutting down...\n", sig);
    cleanup();
    exit(0);
}

/* 添加inode到白名单 */
static int add_to_whitelist(__u32 inode) {
    __u32 value = 1;
    int ret = bpf_map_update_elem(whitemap_fd, &inode, &value, BPF_ANY);
    if (ret != 0) {
        fprintf(stderr, "Failed to add inode %u to whitelist: %s\n", inode, strerror(errno));
        return -1;
    }
    printf("Added inode %u to whitelist\n", inode);
    return 0;
}

/* 获取目录的inode并添加到白名单 */
static int whitelist_directory(const char *path) {
    struct stat st;
    if (stat(path, &st) != 0) {
        fprintf(stderr, "Failed to stat %s: %s\n", path, strerror(errno));
        return -1;
    }

    printf("Directory %s has inode %lu\n", path, st.st_ino);
    return add_to_whitelist((__u32)st.st_ino);
}

int main(int argc, char **argv) {
    struct bpf_program *prog;
    struct bpf_map *map;
    const char *prog_name;
    int err;

    printf("FIM Simple Loader - Protecting /home/<USER>/test\n");
    printf("Press Ctrl+C to stop...\n\n");

    // 设置信号处理器
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 设置libbpf错误和调试回调 - 启用详细输出
    // libbpf_set_print(NULL);

    // 加载BPF对象文件
    obj = bpf_object__open("build/fim_simple.o");
    if (libbpf_get_error(obj)) {
        fprintf(stderr, "ERROR: opening BPF object file failed\n");
        return 1;
    }

    // 加载BPF程序到内核
    err = bpf_object__load(obj);
    if (err) {
        fprintf(stderr, "ERROR: loading BPF object file failed: %s\n", strerror(-err));
        goto cleanup;
    }

    printf("BPF programs loaded successfully.\n");

    // 获取whitemap的文件描述符
    map = bpf_object__find_map_by_name(obj, "whitemap");
    if (!map) {
        fprintf(stderr, "ERROR: finding whitemap failed\n");
        goto cleanup;
    }

    whitemap_fd = bpf_map__fd(map);
    if (whitemap_fd < 0) {
        fprintf(stderr, "ERROR: getting whitemap fd failed\n");
        goto cleanup;
    }

    // 添加系统重要目录到白名单
    printf("Setting up whitelist...\n");
    whitelist_directory("/");
    whitelist_directory("/usr");
    whitelist_directory("/bin");
    whitelist_directory("/sbin");
    whitelist_directory("/lib");
    whitelist_directory("/lib64");
    whitelist_directory("/etc");
    whitelist_directory("/var");
    whitelist_directory("/tmp");
    whitelist_directory("/home");
    whitelist_directory("/home/<USER>");
    // 注意：我们不添加 /home/<USER>/test 到白名单，所以它会被拦截

    // 附加所有LSM程序
    bpf_object__for_each_program(prog, obj) {
        prog_name = bpf_program__name(prog);

        if (strstr(prog_name, "block_")) {
            struct bpf_link *link = bpf_program__attach(prog);
            if (libbpf_get_error(link)) {
                fprintf(stderr, "ERROR: failed to attach program %s: %s\n",
                        prog_name, strerror(-libbpf_get_error(link)));
                goto cleanup;
            }

            links[link_count++] = link;
            printf("Attached LSM program: %s\n", prog_name);
        }
    }

    printf("\nFIM protection is now active!\n");
    printf("All access to /home/<USER>/test will be blocked.\n");
    printf("Check kernel logs with: sudo dmesg | grep FIM\n\n");

    // 保持程序运行
    while (1) {
        sleep(1);
    }

cleanup:
    cleanup();
    return 1;
}