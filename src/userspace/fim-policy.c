#include "common.h"
#include <sys/stat.h>

/* 全局变量 */
int debug_mode = 0;
int verbose_mode = 0;
int policy_map_fd = -1;

/* YAML策略解析结构 */
struct policy_rule {
    char path[FIM_MAX_PATH_LEN];
    char operations[256];
    char action[16];  /* allow 或 deny */
    struct policy_rule *next;
};

/* 路径规范化函数 */
int normalize_path(const char *input, char *output, size_t output_size) {
    if (!input || !output || output_size == 0) {
        return -1;
    }

    /* 简单的路径规范化：去除末尾斜杠，处理相对路径 */
    char *resolved = realpath(input, NULL);
    if (resolved) {
        if (strlen(resolved) >= output_size) {
            free(resolved);
            return -1;
        }
        strcpy(output, resolved);
        free(resolved);
        return 0;
    } else {
        /* 如果realpath失败，直接复制输入路径 */
        if (strlen(input) >= output_size) {
            return -1;
        }
        strcpy(output, input);
        return 0;
    }
}

/* 解析操作字符串 */
__u32 parse_operations(const char *ops_str) {
    __u32 operations = 0;
    char *ops_copy = strdup(ops_str);
    char *token = strtok(ops_copy, ",");
    
    while (token) {
        /* 去除空格 */
        while (*token == ' ') token++;
        char *end = token + strlen(token) - 1;
        while (end > token && *end == ' ') *end-- = '\0';
        
        if (strcmp(token, "read") == 0) {
            operations |= (1 << FIM_OP_OPEN_READ);
        } else if (strcmp(token, "write") == 0) {
            operations |= (1 << FIM_OP_OPEN_WRITE);
        } else if (strcmp(token, "create") == 0) {
            operations |= (1 << FIM_OP_CREATE);
        } else if (strcmp(token, "delete") == 0) {
            operations |= (1 << FIM_OP_DELETE);
        } else if (strcmp(token, "rename") == 0) {
            operations |= (1 << FIM_OP_RENAME);
        } else if (strcmp(token, "chmod") == 0) {
            operations |= (1 << FIM_OP_CHMOD);
        } else if (strcmp(token, "chown") == 0) {
            operations |= (1 << FIM_OP_CHOWN);
        } else if (strcmp(token, "all") == 0) {
            operations = FIM_PERM_ALL;
        }
        token = strtok(NULL, ",");
    }
    
    free(ops_copy);
    return operations;
}

/* 解析YAML策略文件 */
int parse_yaml_policy(const char *filename, struct policy_rule **rules) {
    FILE *file = fopen(filename, "r");
    if (!file) {
        LOG_ERROR("Failed to open policy file: %s", filename);
        return -1;
    }
    
    yaml_parser_t parser;
    yaml_document_t document;
    
    if (!yaml_parser_initialize(&parser)) {
        LOG_ERROR("Failed to initialize YAML parser");
        fclose(file);
        return -1;
    }
    
    yaml_parser_set_input_file(&parser, file);
    
    if (!yaml_parser_load(&parser, &document)) {
        LOG_ERROR("Failed to parse YAML file: %s", filename);
        yaml_parser_delete(&parser);
        fclose(file);
        return -1;
    }
    
    yaml_node_t *root = yaml_document_get_root_node(&document);
    if (!root || root->type != YAML_MAPPING_NODE) {
        LOG_ERROR("Invalid YAML format: root must be a mapping");
        yaml_document_delete(&document);
        yaml_parser_delete(&parser);
        fclose(file);
        return -1;
    }
    
    /* 解析策略规则 */
    for (yaml_node_pair_t *pair = root->data.mapping.pairs.start;
         pair < root->data.mapping.pairs.top; pair++) {
        
        yaml_node_t *key = yaml_document_get_node(&document, pair->key);
        yaml_node_t *value = yaml_document_get_node(&document, pair->value);
        
        if (key->type == YAML_SCALAR_NODE && 
            strcmp((char*)key->data.scalar.value, "rules") == 0) {
            
            if (value->type == YAML_SEQUENCE_NODE) {
                /* 解析规则列表 */
                for (yaml_node_item_t *item = value->data.sequence.items.start;
                     item < value->data.sequence.items.top; item++) {
                    
                    yaml_node_t *rule_node = yaml_document_get_node(&document, *item);
                    if (rule_node->type == YAML_MAPPING_NODE) {
                        struct policy_rule *rule = calloc(1, sizeof(struct policy_rule));
                        
                        /* 解析规则属性 */
                        for (yaml_node_pair_t *rule_pair = rule_node->data.mapping.pairs.start;
                             rule_pair < rule_node->data.mapping.pairs.top; rule_pair++) {
                            
                            yaml_node_t *rule_key = yaml_document_get_node(&document, rule_pair->key);
                            yaml_node_t *rule_value = yaml_document_get_node(&document, rule_pair->value);
                            
                            if (rule_key->type == YAML_SCALAR_NODE && rule_value->type == YAML_SCALAR_NODE) {
                                char *key_str = (char*)rule_key->data.scalar.value;
                                char *value_str = (char*)rule_value->data.scalar.value;
                                
                                if (strcmp(key_str, "path") == 0) {
                                    strncpy(rule->path, value_str, sizeof(rule->path) - 1);
                                } else if (strcmp(key_str, "operations") == 0) {
                                    strncpy(rule->operations, value_str, sizeof(rule->operations) - 1);
                                } else if (strcmp(key_str, "action") == 0) {
                                    strncpy(rule->action, value_str, sizeof(rule->action) - 1);
                                }
                            }
                        }
                        
                        /* 添加到规则链表 */
                        rule->next = *rules;
                        *rules = rule;
                    }
                }
            }
        }
    }
    
    yaml_document_delete(&document);
    yaml_parser_delete(&parser);
    fclose(file);
    
    return 0;
}

/* 应用策略规则到eBPF map */
int apply_policy_rules(struct policy_rule *rules) {
    struct policy_rule *rule = rules;
    int count = 0;
    
    while (rule) {
        struct fim_policy_entry entry;
        struct fim_inode_key key;
        struct stat st;
        char normalized_path[FIM_MAX_PATH_LEN];
        
        /* 规范化路径 */
        if (normalize_path(rule->path, normalized_path, sizeof(normalized_path)) < 0) {
            LOG_ERROR("Failed to normalize path: %s", rule->path);
            rule = rule->next;
            continue;
        }
        
        /* 获取inode信息 */
        if (stat(normalized_path, &st) != 0) {
            LOG_ERROR("Failed to stat file: %s (%s)", normalized_path, strerror(errno));
            rule = rule->next;
            continue;
        }
        
        /* 填充策略条目 */
        memset(&entry, 0, sizeof(entry));
        key.ino = st.st_ino;
        key.dev = st.st_dev;
        entry.operations = parse_operations(rule->operations);
        entry.action = (strcmp(rule->action, "allow") == 0) ? FIM_POLICY_ALLOW : FIM_POLICY_DENY;
        strncpy(entry.description, normalized_path, sizeof(entry.description) - 1);
        
        /* 更新eBPF map */
        if (bpf_map_update_elem(policy_map_fd, &key, &entry, BPF_ANY) != 0) {
            LOG_ERROR("Failed to update policy map for %s: %s", normalized_path, strerror(errno));
        } else {
            const char *action_str = (entry.action == FIM_POLICY_ALLOW) ? "ALLOW" : "DENY";
            LOG_INFO("Applied policy: %s -> %s (inode=%llu, dev=%u, ops=0x%x)",
                     normalized_path, action_str, key.ino, key.dev, entry.operations);
            count++;
        }
        
        rule = rule->next;
    }
    
    return count;
}

/* 释放策略规则链表 */
void free_policy_rules(struct policy_rule *rules) {
    while (rules) {
        struct policy_rule *next = rules->next;
        free(rules);
        rules = next;
    }
}

/* 加载YAML策略文件 */
int load_yaml_policy(const char *filename) {
    struct policy_rule *rules = NULL;
    int result = 0;
    
    LOG_INFO("Loading YAML policy from: %s", filename);
    
    /* 解析YAML文件 */
    if (parse_yaml_policy(filename, &rules) < 0) {
        return -1;
    }
    
    /* 清空现有策略 */
    struct fim_inode_key key, next_key;
    memset(&key, 0, sizeof(key));
    while (bpf_map_get_next_key(policy_map_fd, &key, &next_key) == 0) {
        bpf_map_delete_elem(policy_map_fd, &next_key);
        memcpy(&key, &next_key, sizeof(key));
    }
    
    /* 应用新策略 */
    int count = apply_policy_rules(rules);
    if (count > 0) {
        LOG_INFO("Successfully loaded %d policy rules", count);
    } else {
        LOG_ERROR("No policy rules were loaded");
        result = -1;
    }
    
    /* 清理 */
    free_policy_rules(rules);
    
    return result;
}

/* 打印使用说明 */
void print_usage(const char *prog_name) {
    printf("Usage: %s [OPTIONS] COMMAND [ARGS]\n\n", prog_name);
    printf("Options:\n");
    printf("  -h          Show this help message\n");
    printf("  -v          Verbose mode\n");
    printf("  -d          Debug mode\n\n");
    printf("Commands:\n");
    printf("  load FILE   Load YAML policy file\n");
    printf("  validate FILE Validate YAML policy file\n");
    printf("  show        Show current policy rules\n");
    printf("  clear       Clear all policy rules\n\n");
    printf("Examples:\n");
    printf("  %s load /etc/fim/policy.yaml\n", prog_name);
    printf("  %s validate /etc/fim/policy.yaml\n", prog_name);
    printf("  %s show\n", prog_name);
}

/* 初始化eBPF maps */
int init_bpf_maps(void) {
    policy_map_fd = bpf_obj_get("/sys/fs/bpf/fim_policy_map");
    if (policy_map_fd < 0) {
        LOG_ERROR("Failed to get policy map: %s", strerror(errno));
        return -1;
    }
    return 0;
}

/* 列出策略规则 */
int list_policy_rules(void) {
    struct fim_inode_key key, next_key;
    struct fim_policy_entry entry;
    int count = 0;

    printf("Policy rules:\n");
    printf("%-50s %-10s %-10s %-12s %-8s\n",
           "Path", "Inode", "Device", "Operations", "Action");
    printf("%s\n", "-------------------------------------------------------------------------------");

    memset(&key, 0, sizeof(key));
    while (bpf_map_get_next_key(policy_map_fd, &key, &next_key) == 0) {
        if (bpf_map_lookup_elem(policy_map_fd, &next_key, &entry) == 0) {
            const char *action = (entry.action == FIM_POLICY_ALLOW) ? "ALLOW" : "DENY";

            printf("%-50s %-10llu %-10u 0x%-10x %-8s\n",
                   entry.description, key.ino, key.dev,
                   entry.operations, action);
            count++;
        }
        memcpy(&key, &next_key, sizeof(key));
    }

    printf("\nTotal rules: %d\n", count);
    return 0;
}

/* 清空策略规则 */
int clear_policy_rules(void) {
    struct fim_inode_key key, next_key;
    int count = 0;

    memset(&key, 0, sizeof(key));
    while (bpf_map_get_next_key(policy_map_fd, &key, &next_key) == 0) {
        if (bpf_map_delete_elem(policy_map_fd, &next_key) == 0) {
            count++;
        }
        memcpy(&key, &next_key, sizeof(key));
    }

    LOG_INFO("Cleared %d policy rules", count);
    return 0;
}

/* 主函数 */
int main(int argc, char *argv[]) {
    int opt;
    const char *command = NULL;
    
    /* 解析命令行参数 */
    while ((opt = getopt(argc, argv, "hvd")) != -1) {
        switch (opt) {
            case 'h':
                print_usage(argv[0]);
                return 0;
            case 'v':
                verbose_mode = 1;
                break;
            case 'd':
                debug_mode = 1;
                break;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    if (optind >= argc) {
        print_usage(argv[0]);
        return 1;
    }
    
    command = argv[optind];
    
    /* 初始化eBPF map */
    if (init_bpf_maps() < 0) {
        LOG_ERROR("Failed to initialize BPF maps");
        return 1;
    }
    
    if (strcmp(command, "load") == 0) {
        if (optind + 1 >= argc) {
            LOG_ERROR("Missing policy file argument");
            return 1;
        }
        return load_yaml_policy(argv[optind + 1]);
        
    } else if (strcmp(command, "validate") == 0) {
        if (optind + 1 >= argc) {
            LOG_ERROR("Missing policy file argument");
            return 1;
        }
        struct policy_rule *rules = NULL;
        int result = parse_yaml_policy(argv[optind + 1], &rules);
        if (result == 0) {
            printf("YAML policy file is valid\n");
            free_policy_rules(rules);
        }
        return result;
        
    } else if (strcmp(command, "show") == 0) {
        return list_policy_rules();
        
    } else if (strcmp(command, "clear") == 0) {
        return clear_policy_rules();
        
    } else {
        LOG_ERROR("Unknown command: %s", command);
        print_usage(argv[0]);
        return 1;
    }
    
    return 0;
}
