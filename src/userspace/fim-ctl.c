#include "common.h"
#include <sys/stat.h>

/* 全局变量 */
int debug_mode = 0;
int verbose_mode = 0;
static struct bpf_object *bpf_obj = NULL;
static int policy_map_fd = -1;
static int events_map_fd = -1;
static int stats_map_fd = -1;
static int config_map_fd = -1;

/* 信号处理 */
static volatile int running = 1;

void signal_handler(int sig) {
    switch (sig) {
        case SIGINT:
        case SIGTERM:
            LOG_INFO("Received signal %d, shutting down...", sig);
            running = 0;
            break;
        case SIGHUP:
            LOG_INFO("Received SIGHUP, reloading configuration...");
            /* TODO: 重新加载配置 */
            break;
    }
}

/* 加载eBPF程序 */
int load_ebpf_program(const char *obj_file) {
    int err;
    
    /* 打开eBPF对象文件 */
    bpf_obj = bpf_object__open(obj_file);
    if (libbpf_get_error(bpf_obj)) {
        LOG_ERROR("Failed to open eBPF object file: %s", obj_file);
        return -1;
    }
    
    /* 加载eBPF程序 */
    err = bpf_object__load(bpf_obj);
    if (err) {
        LOG_ERROR("Failed to load eBPF program: %s", strerror(-err));
        bpf_object__close(bpf_obj);
        return -1;
    }
    
    /* 获取map文件描述符 */
    policy_map_fd = bpf_object__find_map_fd_by_name(bpf_obj, "policy_map");
    events_map_fd = bpf_object__find_map_fd_by_name(bpf_obj, "events_map");
    stats_map_fd = bpf_object__find_map_fd_by_name(bpf_obj, "stats_map");
    config_map_fd = bpf_object__find_map_fd_by_name(bpf_obj, "config_map");
    
    if (policy_map_fd < 0 || events_map_fd < 0 ||
        stats_map_fd < 0 || config_map_fd < 0) {
        LOG_ERROR("Failed to find eBPF maps");
        bpf_object__close(bpf_obj);
        return -1;
    }
    
    /* 附加LSM hooks */
    err = attach_lsm_hooks(bpf_obj);
    if (err) {
        LOG_ERROR("Failed to attach LSM hooks");
        bpf_object__close(bpf_obj);
        return -1;
    }
    
    LOG_INFO("eBPF program loaded successfully");
    return 0;
}

/* 附加LSM hooks */
int attach_lsm_hooks(struct bpf_object *obj) {
    struct bpf_program *prog;
    struct bpf_link *link;
    int err = 0;
    
    bpf_object__for_each_program(prog, obj) {
        link = bpf_program__attach(prog);
        if (libbpf_get_error(link)) {
            LOG_ERROR("Failed to attach program %s", bpf_program__name(prog));
            err = -1;
            continue;
        }
        LOG_DEBUG("Attached program: %s", bpf_program__name(prog));
    }
    
    return err;
}

/* 卸载eBPF程序 */
int unload_ebpf_program(void) {
    if (bpf_obj) {
        bpf_object__close(bpf_obj);
        bpf_obj = NULL;
        LOG_INFO("eBPF program unloaded");
    }
    return 0;
}

/* 获取文件的inode和设备号 */
int get_file_inode_info(const char *path, struct fim_inode_key *key, struct stat *st) {
    if (!path || !key) {
        return -1;
    }

    if (stat(path, st) != 0) {
        LOG_ERROR("Failed to stat file: %s (%s)", path, strerror(errno));
        return -1;
    }

    key->inode = st->st_ino;
    key->dev = st->st_dev;

    return 0;
}

/* 添加策略条目 */
int add_policy_entry(const char *path, __u32 operations, __u32 rule_type) {
    struct fim_policy_entry entry;
    struct fim_inode_key key;
    struct stat st;
    char normalized_path[FIM_MAX_PATH_LEN];
    int err;

    if (!path || policy_map_fd < 0) {
        return -1;
    }

    /* 规范化路径 */
    if (normalize_path(path, normalized_path, sizeof(normalized_path)) < 0) {
        LOG_ERROR("Failed to normalize path: %s", path);
        return -1;
    }

    /* 获取inode信息 */
    if (get_file_inode_info(normalized_path, &key, &st) < 0) {
        return -1;
    }

    /* 填充策略条目 */
    memset(&entry, 0, sizeof(entry));
    entry.inode = key.inode;
    entry.dev = key.dev;
    entry.operations = operations;
    entry.rule_type = rule_type;
    strncpy(entry.path, normalized_path, sizeof(entry.path) - 1);

    /* 更新map */
    err = bpf_map_update_elem(policy_map_fd, &key, &entry, BPF_ANY);
    if (err) {
        LOG_ERROR("Failed to add policy entry: %s", strerror(errno));
        return -1;
    }

    const char *type = S_ISDIR(st.st_mode) ? "DIR" : "FILE";
    const char *action = (rule_type == FIM_RULE_ALLOW) ? "ALLOW" : "DENY";
    LOG_INFO("Added policy entry: %s -> %s (inode=%u, dev=%u, type=%s, ops=0x%x)",
             normalized_path, action, key.inode, key.dev, type, operations);
    return 0;
}



/* 获取统计信息 */
int get_stats(struct fim_stats *stats) {
    __u32 key = 0;
    
    if (!stats || stats_map_fd < 0) {
        return -1;
    }
    
    if (bpf_map_lookup_elem(stats_map_fd, &key, stats) != 0) {
        LOG_ERROR("Failed to get statistics");
        return -1;
    }
    
    return 0;
}

/* 重置统计信息 */
int reset_stats(void) {
    __u32 key = 0;
    struct fim_stats stats;
    
    if (stats_map_fd < 0) {
        return -1;
    }
    
    memset(&stats, 0, sizeof(stats));
    if (bpf_map_update_elem(stats_map_fd, &key, &stats, BPF_ANY) != 0) {
        LOG_ERROR("Failed to reset statistics");
        return -1;
    }
    
    LOG_INFO("Statistics reset");
    return 0;
}

/* 主函数 */
int main(int argc, char *argv[]) {
    int opt;
    const char *command = NULL;
    struct fim_config config = {
        .enabled = 1,
        .log_level = FIM_LOG_INFO,
        .default_action = FIM_DENY,
        .max_events = 1000
    };

    /* 解析命令行参数 */
    while ((opt = getopt(argc, argv, "hvdc:")) != -1) {
        switch (opt) {
            case 'h':
                print_usage(argv[0]);
                return 0;
            case 'v':
                verbose_mode = 1;
                break;
            case 'd':
                debug_mode = 1;
                break;
            case 'c':
                command = optarg;
                break;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    /* 检查root权限 */
    if (check_root_privileges() != 0) {
        LOG_ERROR("This program requires root privileges");
        return 1;
    }
    
    /* 设置信号处理 */
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGHUP, signal_handler);
    
    /* 加载eBPF程序 */
    if (load_ebpf_program(FIM_EBPF_OBJ) != 0) {
        return 1;
    }
    


    /* 初始化配置 */
    __u32 key = 0;
    bpf_map_update_elem(config_map_fd, &key, &config, BPF_ANY);
    
    /* 执行命令 */
    if (command) {
        if (strcmp(command, "stats") == 0) {
            struct fim_stats stats;
            if (get_stats(&stats) == 0) {
                printf("Statistics:\n");
                printf("  Total events: %llu\n", stats.total_events);
                printf("  Allowed: %llu\n", stats.allowed_events);
                printf("  Denied: %llu\n", stats.denied_events);
                printf("  List hits: %llu\n", stats.whitelist_hits);
                printf("  List misses: %llu\n", stats.whitelist_misses);
            }
            return 0;

        } else if (strcmp(command, "daemon") == 0) {
            /* 守护进程模式 */
            if (daemonize() != 0) {
                return 1;
            }
            create_pid_file(FIM_PID_FILE);
            
            /* 主循环 */
            while (running) {
                sleep(1);
            }
            
            remove_pid_file(FIM_PID_FILE);
            unload_ebpf_program();
            return 0;
        }
    }
    
    print_usage(argv[0]);
    unload_ebpf_program();
    return 1;
}

/* 辅助函数实现 */

/* 规范化路径 */
int normalize_path(const char *input, char *output, size_t output_size) {
    if (!input || !output || output_size == 0) {
        return -1;
    }

    /* 简单的路径规范化 - 实际实现可能需要更复杂的逻辑 */
    if (realpath(input, output) == NULL) {
        /* 如果realpath失败，直接复制输入路径 */
        strncpy(output, input, output_size - 1);
        output[output_size - 1] = '\0';
    }

    return 0;
}

/* 解析操作字符串 */
__u32 parse_operations(const char *ops_str) {
    __u32 operations = 0;

    if (!ops_str) {
        return FIM_PERM_ALL;
    }

    if (strstr(ops_str, "read") || strstr(ops_str, "r")) {
        operations |= FIM_PERM_READ;
    }
    if (strstr(ops_str, "write") || strstr(ops_str, "w")) {
        operations |= FIM_PERM_WRITE;
    }
    if (strstr(ops_str, "create") || strstr(ops_str, "c")) {
        operations |= FIM_PERM_CREATE;
    }
    if (strstr(ops_str, "delete") || strstr(ops_str, "d")) {
        operations |= FIM_PERM_DELETE;
    }
    if (strstr(ops_str, "rename")) {
        operations |= FIM_PERM_RENAME;
    }
    if (strstr(ops_str, "chmod")) {
        operations |= FIM_PERM_CHMOD;
    }
    if (strstr(ops_str, "chown")) {
        operations |= FIM_PERM_CHOWN;
    }
    if (strstr(ops_str, "all")) {
        operations = FIM_PERM_ALL;
    }

    return operations ? operations : FIM_PERM_ALL;
}

/* 检查root权限 */
int check_root_privileges(void) {
    return (geteuid() != 0) ? -1 : 0;
}

/* 守护进程化 */
int daemonize(void) {
    pid_t pid;

    /* 第一次fork */
    pid = fork();
    if (pid < 0) {
        return -1;
    }
    if (pid > 0) {
        exit(0); /* 父进程退出 */
    }

    /* 创建新会话 */
    if (setsid() < 0) {
        return -1;
    }

    /* 第二次fork */
    pid = fork();
    if (pid < 0) {
        return -1;
    }
    if (pid > 0) {
        exit(0);
    }

    /* 改变工作目录 */
    chdir("/");

    /* 关闭文件描述符 */
    close(STDIN_FILENO);
    close(STDOUT_FILENO);
    close(STDERR_FILENO);

    return 0;
}

/* 创建PID文件 */
int create_pid_file(const char *pid_file) {
    FILE *fp;

    fp = fopen(pid_file, "w");
    if (!fp) {
        return -1;
    }

    fprintf(fp, "%d\n", getpid());
    fclose(fp);

    return 0;
}

/* 删除PID文件 */
int remove_pid_file(const char *pid_file) {
    return unlink(pid_file);
}

/* 打印使用说明 */
void print_usage(const char *prog_name) {
    printf("Usage: %s [OPTIONS] -c COMMAND [ARGS]\n\n", prog_name);
    printf("Options:\n");
    printf("  -h          Show this help message\n");
    printf("  -v          Verbose mode\n");
    printf("  -d          Debug mode\n");
    printf("  -c COMMAND  Command to execute\n");
    printf("  -p PATH     File path (for add/del commands)\n");
    printf("  -o OPS      Operations (for add command)\n\n");
    printf("Commands:\n");
    printf("  stats       Show statistics\n");
    printf("  daemon      Run as daemon\n\n");
    printf("Operations (for add command):\n");
    printf("  read, write, create, delete, rename, chmod, chown, all\n\n");
    printf("Examples:\n");
    printf("  %s -c stats                      # Show statistics\n", prog_name);
    printf("  %s -c daemon                     # Run as daemon\n", prog_name);
    printf("\nNote: Policy management is done via fim-policy tool\n");
}
