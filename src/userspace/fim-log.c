#include "common.h"

/* 全局变量 */
int debug_mode = 0;
int verbose_mode = 0;
static volatile int running = 1;

/* 信号处理 */
void signal_handler(int sig) {
    switch (sig) {
        case SIGINT:
        case SIGTERM:
            LOG_INFO("Received signal %d, shutting down...", sig);
            running = 0;
            break;
    }
}

/* 操作类型转字符串 */
const char *operation_to_string(__u32 op) {
    switch (op) {
        case FIM_OP_OPEN_READ:  return "READ";
        case FIM_OP_OPEN_WRITE: return "WRITE";
        case FIM_OP_CREATE:     return "CREATE";
        case FIM_OP_DELETE:     return "DELETE";
        case FIM_OP_RENAME:     return "RENAME";
        case FIM_OP_CHMOD:      return "CHMOD";
        case FIM_OP_CHOWN:      return "CHOWN";
        case FIM_OP_TRUNCATE:   return "TRUNCATE";
        default:                return "UNKNOWN";
    }
}

/* 决策转字符串 */
const char *decision_to_string(__u32 decision) {
    return (decision == FIM_ALLOW) ? "ALLOW" : "DENY";
}

/* 格式化时间戳 */
void format_timestamp(__u64 timestamp, char *buf, size_t buf_size) {
    time_t sec = timestamp / 1000000000ULL;
    struct tm *tm_info = localtime(&sec);
    
    strftime(buf, buf_size, "%Y-%m-%d %H:%M:%S", tm_info);
}

/* 处理事件 */
int handle_event(void *ctx, void *data, size_t data_sz) {
    struct fim_event *event = (struct fim_event *)data;
    char timestamp_str[64];
    FILE *log_file = (FILE *)ctx;
    
    if (!event || data_sz < sizeof(*event)) {
        return -1;
    }
    
    format_timestamp(event->timestamp, timestamp_str, sizeof(timestamp_str));
    
    /* 输出到控制台 */
    if (verbose_mode) {
        printf("[%s] PID:%u UID:%u GID:%u COMM:%s OP:%s DECISION:%s INODE:%u DEV:%u PATH:%s\n",
               timestamp_str,
               event->pid,
               event->uid,
               event->gid,
               event->comm,
               operation_to_string(event->operation),
               decision_to_string(event->decision),
               event->inode,
               event->dev,
               event->path);
    }

    /* 写入日志文件 */
    if (log_file) {
        fprintf(log_file, "[%s] PID:%u UID:%u GID:%u COMM:%s OP:%s DECISION:%s INODE:%u DEV:%u PATH:%s\n",
                timestamp_str,
                event->pid,
                event->uid,
                event->gid,
                event->comm,
                operation_to_string(event->operation),
                decision_to_string(event->decision),
                event->inode,
                event->dev,
                event->path);
        fflush(log_file);
    }
    
    return 0;
}

/* 监控事件 */
int monitor_events(void) {
    struct bpf_object *obj;
    struct ring_buffer *rb = NULL;
    int events_map_fd;
    FILE *log_file = NULL;
    int err = 0;
    
    /* 打开eBPF对象 */
    obj = bpf_object__open(FIM_EBPF_OBJ);
    if (libbpf_get_error(obj)) {
        LOG_ERROR("Failed to open eBPF object");
        return -1;
    }
    
    /* 加载eBPF程序 */
    err = bpf_object__load(obj);
    if (err) {
        LOG_ERROR("Failed to load eBPF program");
        goto cleanup;
    }
    
    /* 获取events map */
    events_map_fd = bpf_object__find_map_fd_by_name(obj, "events_map");
    if (events_map_fd < 0) {
        LOG_ERROR("Failed to find events map");
        err = -1;
        goto cleanup;
    }
    
    /* 打开日志文件 */
    log_file = fopen(FIM_LOG_FILE, "a");
    if (!log_file) {
        LOG_WARN("Failed to open log file: %s", FIM_LOG_FILE);
    }
    
    /* 创建ring buffer */
    rb = ring_buffer__new(events_map_fd, handle_event, log_file, NULL);
    if (!rb) {
        LOG_ERROR("Failed to create ring buffer");
        err = -1;
        goto cleanup;
    }
    
    LOG_INFO("Starting event monitoring...");
    
    /* 主循环 */
    while (running) {
        err = ring_buffer__poll(rb, 100); /* 100ms timeout */
        if (err == -EINTR) {
            break;
        }
        if (err < 0) {
            LOG_ERROR("Error polling ring buffer: %d", err);
            break;
        }
    }
    
cleanup:
    if (rb) {
        ring_buffer__free(rb);
    }
    if (log_file) {
        fclose(log_file);
    }
    if (obj) {
        bpf_object__close(obj);
    }
    
    return err;
}

/* 打印使用说明 */
void print_usage(const char *prog_name) {
    printf("Usage: %s [OPTIONS]\n\n", prog_name);
    printf("Options:\n");
    printf("  -h          Show this help message\n");
    printf("  -v          Verbose mode (print to console)\n");
    printf("  -d          Debug mode\n");
    printf("  -f FILE     Log file path (default: %s)\n", FIM_LOG_FILE);
    printf("  -D          Run as daemon\n\n");
    printf("Description:\n");
    printf("  Monitor and log file access control events from the FIM system.\n");
    printf("  Events are logged to the specified file and optionally to console.\n\n");
    printf("Examples:\n");
    printf("  %s -v                    # Monitor with console output\n", prog_name);
    printf("  %s -D                    # Run as daemon\n", prog_name);
    printf("  %s -v -f /tmp/fim.log    # Custom log file with console output\n", prog_name);
}

/* 守护进程化 */
int daemonize(void) {
    pid_t pid;
    
    pid = fork();
    if (pid < 0) {
        return -1;
    }
    if (pid > 0) {
        exit(0);
    }
    
    if (setsid() < 0) {
        return -1;
    }
    
    pid = fork();
    if (pid < 0) {
        return -1;
    }
    if (pid > 0) {
        exit(0);
    }
    
    chdir("/");
    close(STDIN_FILENO);
    close(STDOUT_FILENO);
    close(STDERR_FILENO);
    
    return 0;
}

/* 主函数 */
int main(int argc, char *argv[]) {
    int opt;
    int daemon_mode = 0;
    const char *log_file_path = FIM_LOG_FILE;
    
    /* 解析命令行参数 */
    while ((opt = getopt(argc, argv, "hvdDf:")) != -1) {
        switch (opt) {
            case 'h':
                print_usage(argv[0]);
                return 0;
            case 'v':
                verbose_mode = 1;
                break;
            case 'd':
                debug_mode = 1;
                break;
            case 'D':
                daemon_mode = 1;
                break;
            case 'f':
                log_file_path = optarg;
                break;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    /* 检查root权限 */
    if (geteuid() != 0) {
        LOG_ERROR("This program requires root privileges");
        return 1;
    }
    
    /* 设置信号处理 */
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    /* 守护进程模式 */
    if (daemon_mode) {
        if (daemonize() != 0) {
            LOG_ERROR("Failed to daemonize");
            return 1;
        }
        verbose_mode = 0; /* 守护进程模式下不输出到控制台 */
    }
    
    /* 开始监控事件 */
    int ret = monitor_events();
    
    LOG_INFO("Event monitoring stopped");
    return ret;
}
