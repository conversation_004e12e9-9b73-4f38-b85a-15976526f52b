#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <errno.h>
#include <string.h>
#include <sys/stat.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>

static struct bpf_object *obj = NULL;
static struct bpf_link *links[10];
static int link_count = 0;
static int blacklist_fd = -1;
static volatile int running = 1;

static void cleanup(void) {
    printf("Cleaning up...\n");

    // 卸载所有BPF链接
    for (int i = 0; i < link_count; i++) {
        if (links[i]) {
            bpf_link__destroy(links[i]);
        }
    }

    // 关闭BPF对象
    if (obj) {
        bpf_object__close(obj);
    }

    printf("FIM protection disabled.\n");
}

static void signal_handler(int sig) {
    printf("\nReceived signal %d, shutting down...\n", sig);
    running = 0;
}

/* 添加目录到黑名单 */
static int add_directory_to_blacklist(const char *path) {
    struct stat st;
    if (stat(path, &st) != 0) {
        fprintf(stderr, "Failed to stat %s: %s\n", path, strerror(errno));
        return -1;
    }

    __u32 inode = (__u32)st.st_ino;
    __u32 value = 1;
    int ret = bpf_map_update_elem(blacklist_fd, &inode, &value, BPF_ANY);
    if (ret != 0) {
        fprintf(stderr, "Failed to add inode %u to blacklist: %s\n", inode, strerror(errno));
        return -1;
    }

    printf("✓ Blocked: %s (inode: %u)\n", path, inode);
    return 0;
}

/* 加载eBPF程序 */
static int load_ebpf_program(void) {
    struct bpf_program *prog;
    struct bpf_map *map;
    const char *prog_name;
    int err;

    // 加载BPF对象文件
    obj = bpf_object__open("/home/<USER>/FIM/build/fim_lsm.o");
    if (libbpf_get_error(obj)) {
        fprintf(stderr, "ERROR: opening BPF object file failed\n");
        return -1;
    }

    // 加载BPF程序到内核
    err = bpf_object__load(obj);
    if (err) {
        fprintf(stderr, "ERROR: loading BPF object file failed: %s\n", strerror(-err));
        return -1;
    }

    printf("✓ BPF programs loaded successfully.\n");

    // 获取blacklist的文件描述符
    map = bpf_object__find_map_by_name(obj, "blacklist_map");
    if (!map) {
        fprintf(stderr, "ERROR: finding blacklist map failed\n");
        return -1;
    }

    blacklist_fd = bpf_map__fd(map);
    if (blacklist_fd < 0) {
        fprintf(stderr, "ERROR: getting blacklist fd failed\n");
        return -1;
    }

    // 附加所有LSM程序
    bpf_object__for_each_program(prog, obj) {
        prog_name = bpf_program__name(prog);

        if (strstr(prog_name, "fim_")) {
            struct bpf_link *link = bpf_program__attach(prog);
            if (libbpf_get_error(link)) {
                fprintf(stderr, "ERROR: failed to attach program %s: %s\n",
                        prog_name, strerror(-libbpf_get_error(link)));
                return -1;
            }

            links[link_count++] = link;
            printf("✓ Attached LSM program: %s\n", prog_name);
        }
    }

    return 0;
}

static void print_usage(const char *prog_name) {
    printf("Usage: %s <command> [options]\n", prog_name);
    printf("\nCommands:\n");
    printf("  start                 Start FIM protection\n");
    printf("  add <path>           Add directory to blacklist\n");
    printf("\nExamples:\n");
    printf("  %s start\n", prog_name);
    printf("  %s add /home/<USER>/sensitive\n", prog_name);
}

int main(int argc, char **argv) {
    if (argc < 2) {
        print_usage(argv[0]);
        return 1;
    }

    const char *command = argv[1];

    // 设置信号处理器
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    if (strcmp(command, "start") == 0) {
        printf("Starting FIM blacklist protection...\n");

        if (load_ebpf_program() != 0) {
            fprintf(stderr, "Failed to load eBPF program\n");
            return 1;
        }

        // 添加默认黑名单目录
        add_directory_to_blacklist("/home/<USER>/test");

        printf("\n🛡️  FIM protection is now active!\n");
        printf("📋 Check blocked access with: sudo cat /sys/kernel/debug/tracing/trace_pipe\n");
        printf("Press Ctrl+C to stop...\n\n");

        // 保持程序运行
        while (running) {
            sleep(1);
        }

        cleanup();

    } else {
        fprintf(stderr, "Unknown command: %s\n", command);
        print_usage(argv[0]);
        return 1;
    }

    return 0;
}