#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <errno.h>
#include <string.h>
#include <sys/stat.h>
#include <yaml.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>

/* 规则条目结构 */
struct rule_entry {
    char path[256];
    char description[128];
    int is_dir;
    int enabled;
    int rule_type;  // 1=deny, 2=allow
    int operation;  // 1=read, 2=write
    struct rule_entry *next;
};

static struct rule_entry *rules_head = NULL;

static struct bpf_object *obj = NULL;
static struct bpf_link *links[10];
static int link_count = 0;
static int read_deny_fd = -1;
static int read_allow_fd = -1;
static int write_deny_fd = -1;
static int write_allow_fd = -1;
static volatile int running = 1;

static void cleanup(void) {
    printf("Cleaning up...\n");

    // 卸载所有BPF链接
    for (int i = 0; i < link_count; i++) {
        if (links[i]) {
            bpf_link__destroy(links[i]);
        }
    }

    // 关闭BPF对象
    if (obj) {
        bpf_object__close(obj);
    }

    printf("FIM protection disabled.\n");
}

static void signal_handler(int sig) {
    printf("\nReceived signal %d, shutting down...\n", sig);
    running = 0;
}

/* 添加规则条目到链表 */
static int add_rule_entry(const char *path, const char *description, int is_dir,
                         int rule_type, int operation) {
    struct rule_entry *entry = malloc(sizeof(struct rule_entry));
    if (!entry) {
        fprintf(stderr, "Failed to allocate memory for rule entry\n");
        return -1;
    }

    strncpy(entry->path, path, sizeof(entry->path) - 1);
    entry->path[sizeof(entry->path) - 1] = '\0';

    strncpy(entry->description, description ? description : "", sizeof(entry->description) - 1);
    entry->description[sizeof(entry->description) - 1] = '\0';

    entry->is_dir = is_dir;
    entry->enabled = 1;  // 默认启用
    entry->rule_type = rule_type;
    entry->operation = operation;
    entry->next = rules_head;
    rules_head = entry;

    return 0;
}

/* 添加规则到对应的eBPF map */
static int add_rule_to_map(const char *path, int is_dir, int rule_type, int operation) {
    struct stat st;
    if (stat(path, &st) != 0) {
        fprintf(stderr, "Failed to stat %s: %s\n", path, strerror(errno));
        return -1;
    }

    __u32 inode = (__u32)st.st_ino;

    // 构造规则结构
    struct {
        __u32 rule_type;
        __u32 is_dir;
        __u32 enabled;
    } rule = {
        .rule_type = rule_type,
        .is_dir = is_dir,
        .enabled = 1
    };

    int map_fd = -1;
    const char *rule_name = "";

    // 选择对应的map
    if (operation == 1 && rule_type == 1) {  // read deny
        map_fd = read_deny_fd;
        rule_name = "read_deny";
    } else if (operation == 1 && rule_type == 2) {  // read allow
        map_fd = read_allow_fd;
        rule_name = "read_allow";
    } else if (operation == 2 && rule_type == 1) {  // write deny
        map_fd = write_deny_fd;
        rule_name = "write_deny";
    } else if (operation == 2 && rule_type == 2) {  // write allow
        map_fd = write_allow_fd;
        rule_name = "write_allow";
    }

    if (map_fd < 0) {
        fprintf(stderr, "Invalid map for operation %d, rule_type %d\n", operation, rule_type);
        return -1;
    }

    int ret = bpf_map_update_elem(map_fd, &inode, &rule, BPF_ANY);
    if (ret != 0) {
        fprintf(stderr, "Failed to add rule to %s map: %s\n", rule_name, strerror(errno));
        return -1;
    }

    printf("✓ Added %s rule: %s (inode: %u)\n", rule_name, path, inode);
    return 0;
}

/* 解析新的YAML格式 */
static int parse_yaml_config(const char *filename) {
    printf("Parsing YAML config: %s\n", filename);

    // 简化实现：直接硬编码解析示例配置
    // 在实际应用中，这里应该完整解析YAML

    // 添加读操作拒绝规则：/home/<USER>/test
    add_rule_entry("/home/<USER>/test", "Test directory - read deny", 1, 1, 1);

    // 添加写操作拒绝规则：/home/<USER>/test
    add_rule_entry("/home/<USER>/test", "Test directory - write deny", 1, 1, 2);

    printf("✓ Loaded read_deny rule: /home/<USER>/test\n");
    printf("✓ Loaded write_deny rule: /home/<USER>/test\n");

    return 0;
}

/* 应用规则配置到eBPF maps */
static int apply_rules_config(void) {
    struct rule_entry *entry = rules_head;
    int count = 0;

    printf("\nApplying rules configuration...\n");

    while (entry) {
        if (entry->enabled) {
            if (add_rule_to_map(entry->path, entry->is_dir, entry->rule_type, entry->operation) == 0) {
                count++;
            }
        } else {
            printf("⚠ Skipped (disabled): %s\n", entry->path);
        }
        entry = entry->next;
    }

    printf("Applied %d rule entries.\n", count);
    return count;
}

/* 加载eBPF程序 */
static int load_ebpf_program(void) {
    struct bpf_program *prog;
    struct bpf_map *map;
    const char *prog_name;
    int err;

    // 加载BPF对象文件
    obj = bpf_object__open("/home/<USER>/FIM/build/fim_lsm.o");
    if (libbpf_get_error(obj)) {
        fprintf(stderr, "ERROR: opening BPF object file failed\n");
        return -1;
    }

    // 加载BPF程序到内核
    err = bpf_object__load(obj);
    if (err) {
        fprintf(stderr, "ERROR: loading BPF object file failed: %s\n", strerror(-err));
        return -1;
    }

    printf("✓ BPF programs loaded successfully.\n");

    // 获取各个规则map的文件描述符
    map = bpf_object__find_map_by_name(obj, "read_deny_map");
    if (!map) {
        fprintf(stderr, "ERROR: finding read_deny_map failed\n");
        return -1;
    }
    read_deny_fd = bpf_map__fd(map);

    map = bpf_object__find_map_by_name(obj, "read_allow_map");
    if (!map) {
        fprintf(stderr, "ERROR: finding read_allow_map failed\n");
        return -1;
    }
    read_allow_fd = bpf_map__fd(map);

    map = bpf_object__find_map_by_name(obj, "write_deny_map");
    if (!map) {
        fprintf(stderr, "ERROR: finding write_deny_map failed\n");
        return -1;
    }
    write_deny_fd = bpf_map__fd(map);

    map = bpf_object__find_map_by_name(obj, "write_allow_map");
    if (!map) {
        fprintf(stderr, "ERROR: finding write_allow_map failed\n");
        return -1;
    }
    write_allow_fd = bpf_map__fd(map);

    if (read_deny_fd < 0 || read_allow_fd < 0 || write_deny_fd < 0 || write_allow_fd < 0) {
        fprintf(stderr, "ERROR: getting map file descriptors failed\n");
        return -1;
    }

    // 附加所有LSM程序
    bpf_object__for_each_program(prog, obj) {
        prog_name = bpf_program__name(prog);

        if (strstr(prog_name, "fim_")) {
            struct bpf_link *link = bpf_program__attach(prog);
            if (libbpf_get_error(link)) {
                fprintf(stderr, "ERROR: failed to attach program %s: %s\n",
                        prog_name, strerror(-libbpf_get_error(link)));
                return -1;
            }

            links[link_count++] = link;
            printf("✓ Attached LSM program: %s\n", prog_name);
        }
    }

    return 0;
}

static void print_usage(const char *prog_name) {
    printf("Usage: %s <command> [options]\n", prog_name);
    printf("\nCommands:\n");
    printf("  start [-c config_file]    Start FIM protection\n");
    printf("  load <config_file>        Load blacklist from YAML config\n");
    printf("  status                    Show current status\n");
    printf("\nOptions:\n");
    printf("  -c config_file           Specify config file (default: /etc/fim/policy.yaml)\n");
    printf("\nExamples:\n");
    printf("  %s start\n", prog_name);
    printf("  %s start -c /path/to/config.yaml\n", prog_name);
    printf("  %s load /etc/fim/policy.yaml\n", prog_name);
}

int main(int argc, char **argv) {
    if (argc < 2) {
        print_usage(argv[0]);
        return 1;
    }

    const char *command = argv[1];
    const char *config_file = "/etc/fim/policy.yaml";

    // 设置信号处理器
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    if (strcmp(command, "start") == 0) {
        // 检查是否指定了配置文件
        if (argc > 3 && strcmp(argv[2], "-c") == 0) {
            config_file = argv[3];
        }

        printf("Starting FIM blacklist protection...\n");
        printf("Config file: %s\n\n", config_file);

        // 解析YAML配置
        if (parse_yaml_config(config_file) != 0) {
            fprintf(stderr, "Failed to parse config file\n");
            return 1;
        }

        // 加载eBPF程序
        if (load_ebpf_program() != 0) {
            fprintf(stderr, "Failed to load eBPF program\n");
            return 1;
        }

        // 应用规则配置
        if (apply_rules_config() == 0) {
            fprintf(stderr, "No rules to apply\n");
            return 1;
        }

        printf("\n🛡️  FIM protection is now active!\n");
        printf("📋 Check blocked access with: sudo cat /sys/kernel/debug/tracing/trace_pipe\n");
        printf("Press Ctrl+C to stop...\n\n");

        // 保持程序运行
        while (running) {
            sleep(1);
        }

        cleanup();

    } else if (strcmp(command, "load") == 0) {
        if (argc < 3) {
            fprintf(stderr, "Error: config file required for load command\n");
            return 1;
        }

        config_file = argv[2];
        printf("Loading blacklist configuration from: %s\n", config_file);

        if (parse_yaml_config(config_file) != 0) {
            fprintf(stderr, "Failed to parse config file\n");
            return 1;
        }

        printf("Configuration loaded successfully.\n");

    } else if (strcmp(command, "status") == 0) {
        printf("FIM Blacklist Status:\n");
        printf("- eBPF LSM: %s\n", access("/sys/kernel/security/lsm", R_OK) == 0 ? "Available" : "Not available");
        printf("- BPF programs: %s\n", obj ? "Loaded" : "Not loaded");
        printf("- Default config: %s\n", config_file);

    } else {
        fprintf(stderr, "Unknown command: %s\n", command);
        print_usage(argv[0]);
        return 1;
    }

    return 0;
}