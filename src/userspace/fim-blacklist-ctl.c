#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <errno.h>
#include <string.h>
#include <sys/stat.h>
#include <yaml.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>

/* 黑名单目录结构 */
struct blacklist_entry {
    char path[256];
    char description[128];
    int enabled;
    struct blacklist_entry *next;
};

static struct blacklist_entry *blacklist_head = NULL;

static struct bpf_object *obj = NULL;
static struct bpf_link *links[10];
static int link_count = 0;
static int blacklist_fd = -1;
static volatile int running = 1;

static void cleanup(void) {
    printf("Cleaning up...\n");

    // 卸载所有BPF链接
    for (int i = 0; i < link_count; i++) {
        if (links[i]) {
            bpf_link__destroy(links[i]);
        }
    }

    // 关闭BPF对象
    if (obj) {
        bpf_object__close(obj);
    }

    printf("FIM protection disabled.\n");
}

static void signal_handler(int sig) {
    printf("\nReceived signal %d, shutting down...\n", sig);
    running = 0;
}

/* 添加黑名单条目到链表 */
static int add_blacklist_entry(const char *path, const char *description, int enabled) {
    struct blacklist_entry *entry = malloc(sizeof(struct blacklist_entry));
    if (!entry) {
        fprintf(stderr, "Failed to allocate memory for blacklist entry\n");
        return -1;
    }

    strncpy(entry->path, path, sizeof(entry->path) - 1);
    entry->path[sizeof(entry->path) - 1] = '\0';

    strncpy(entry->description, description ? description : "", sizeof(entry->description) - 1);
    entry->description[sizeof(entry->description) - 1] = '\0';

    entry->enabled = enabled;
    entry->next = blacklist_head;
    blacklist_head = entry;

    return 0;
}

/* 添加目录到eBPF黑名单map */
static int add_directory_to_blacklist_map(const char *path) {
    struct stat st;
    if (stat(path, &st) != 0) {
        fprintf(stderr, "Failed to stat %s: %s\n", path, strerror(errno));
        return -1;
    }

    __u32 inode = (__u32)st.st_ino;
    __u32 value = 1;
    int ret = bpf_map_update_elem(blacklist_fd, &inode, &value, BPF_ANY);
    if (ret != 0) {
        fprintf(stderr, "Failed to add inode %u to blacklist: %s\n", inode, strerror(errno));
        return -1;
    }

    printf("✓ Blocked: %s (inode: %u)\n", path, inode);
    return 0;
}

/* 简化的YAML解析 - 只解析我们需要的格式 */
static int parse_yaml_config(const char *filename) {
    FILE *file = fopen(filename, "r");
    if (!file) {
        fprintf(stderr, "Failed to open config file: %s\n", filename);
        return -1;
    }

    yaml_parser_t parser;
    yaml_document_t document;

    if (!yaml_parser_initialize(&parser)) {
        fprintf(stderr, "Failed to initialize YAML parser\n");
        fclose(file);
        return -1;
    }

    yaml_parser_set_input_file(&parser, file);

    if (!yaml_parser_load(&parser, &document)) {
        fprintf(stderr, "Failed to parse YAML file\n");
        yaml_parser_delete(&parser);
        fclose(file);
        return -1;
    }

    yaml_node_t *root = yaml_document_get_root_node(&document);
    if (!root || root->type != YAML_MAPPING_NODE) {
        fprintf(stderr, "Invalid YAML format\n");
        goto cleanup;
    }

    // 简化解析：直接查找 blacklist -> directories
    yaml_node_pair_t *pair;
    for (pair = root->data.mapping.pairs.start; pair < root->data.mapping.pairs.top; pair++) {
        yaml_node_t *key = yaml_document_get_node(&document, pair->key);
        yaml_node_t *value = yaml_document_get_node(&document, pair->value);

        if (key && key->type == YAML_SCALAR_NODE &&
            strcmp((char*)key->data.scalar.value, "blacklist") == 0 &&
            value && value->type == YAML_MAPPING_NODE) {

            // 查找directories
            yaml_node_pair_t *bl_pair;
            for (bl_pair = value->data.mapping.pairs.start; bl_pair < value->data.mapping.pairs.top; bl_pair++) {
                yaml_node_t *bl_key = yaml_document_get_node(&document, bl_pair->key);
                yaml_node_t *bl_value = yaml_document_get_node(&document, bl_pair->value);

                if (bl_key && bl_key->type == YAML_SCALAR_NODE &&
                    strcmp((char*)bl_key->data.scalar.value, "directories") == 0 &&
                    bl_value && bl_value->type == YAML_SEQUENCE_NODE) {

                    // 解析目录列表
                    yaml_node_item_t *item;
                    for (item = bl_value->data.sequence.items.start;
                         item < bl_value->data.sequence.items.top; item++) {

                        yaml_node_t *dir_node = yaml_document_get_node(&document, *item);
                        if (dir_node && dir_node->type == YAML_MAPPING_NODE) {
                            char path[256] = "";
                            char description[128] = "";

                            // 解析path和description
                            yaml_node_pair_t *dir_pair;
                            for (dir_pair = dir_node->data.mapping.pairs.start;
                                 dir_pair < dir_node->data.mapping.pairs.top; dir_pair++) {

                                yaml_node_t *attr_key = yaml_document_get_node(&document, dir_pair->key);
                                yaml_node_t *attr_value = yaml_document_get_node(&document, dir_pair->value);

                                if (attr_key && attr_key->type == YAML_SCALAR_NODE &&
                                    attr_value && attr_value->type == YAML_SCALAR_NODE) {

                                    if (strcmp((char*)attr_key->data.scalar.value, "path") == 0) {
                                        strncpy(path, (char*)attr_value->data.scalar.value, sizeof(path) - 1);
                                    } else if (strcmp((char*)attr_key->data.scalar.value, "description") == 0) {
                                        strncpy(description, (char*)attr_value->data.scalar.value, sizeof(description) - 1);
                                    }
                                }
                            }

                            if (strlen(path) > 0) {
                                add_blacklist_entry(path, description, 1);  // 默认启用
                                printf("✓ Loaded: %s - %s\n", path, description);
                            }
                        }
                    }
                }
            }
        }
    }

cleanup:
    yaml_document_delete(&document);
    yaml_parser_delete(&parser);
    fclose(file);
    return 0;
}

/* 应用黑名单配置到eBPF map */
static int apply_blacklist_config(void) {
    struct blacklist_entry *entry = blacklist_head;
    int count = 0;

    printf("\nApplying blacklist configuration...\n");

    while (entry) {
        if (entry->enabled) {
            if (add_directory_to_blacklist_map(entry->path) == 0) {
                count++;
            }
        } else {
            printf("⚠ Skipped (disabled): %s\n", entry->path);
        }
        entry = entry->next;
    }

    printf("Applied %d blacklist entries.\n", count);
    return count;
}

/* 加载eBPF程序 */
static int load_ebpf_program(void) {
    struct bpf_program *prog;
    struct bpf_map *map;
    const char *prog_name;
    int err;

    // 加载BPF对象文件
    obj = bpf_object__open("/home/<USER>/FIM/build/fim_lsm.o");
    if (libbpf_get_error(obj)) {
        fprintf(stderr, "ERROR: opening BPF object file failed\n");
        return -1;
    }

    // 加载BPF程序到内核
    err = bpf_object__load(obj);
    if (err) {
        fprintf(stderr, "ERROR: loading BPF object file failed: %s\n", strerror(-err));
        return -1;
    }

    printf("✓ BPF programs loaded successfully.\n");

    // 获取blacklist的文件描述符
    map = bpf_object__find_map_by_name(obj, "blacklist_map");
    if (!map) {
        fprintf(stderr, "ERROR: finding blacklist map failed\n");
        return -1;
    }

    blacklist_fd = bpf_map__fd(map);
    if (blacklist_fd < 0) {
        fprintf(stderr, "ERROR: getting blacklist fd failed\n");
        return -1;
    }

    // 附加所有LSM程序
    bpf_object__for_each_program(prog, obj) {
        prog_name = bpf_program__name(prog);

        if (strstr(prog_name, "fim_")) {
            struct bpf_link *link = bpf_program__attach(prog);
            if (libbpf_get_error(link)) {
                fprintf(stderr, "ERROR: failed to attach program %s: %s\n",
                        prog_name, strerror(-libbpf_get_error(link)));
                return -1;
            }

            links[link_count++] = link;
            printf("✓ Attached LSM program: %s\n", prog_name);
        }
    }

    return 0;
}

static void print_usage(const char *prog_name) {
    printf("Usage: %s <command> [options]\n", prog_name);
    printf("\nCommands:\n");
    printf("  start [-c config_file]    Start FIM protection\n");
    printf("  load <config_file>        Load blacklist from YAML config\n");
    printf("  status                    Show current status\n");
    printf("\nOptions:\n");
    printf("  -c config_file           Specify config file (default: /etc/fim/policy.yaml)\n");
    printf("\nExamples:\n");
    printf("  %s start\n", prog_name);
    printf("  %s start -c /path/to/config.yaml\n", prog_name);
    printf("  %s load /etc/fim/policy.yaml\n", prog_name);
}

int main(int argc, char **argv) {
    if (argc < 2) {
        print_usage(argv[0]);
        return 1;
    }

    const char *command = argv[1];
    const char *config_file = "/etc/fim/policy.yaml";

    // 设置信号处理器
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    if (strcmp(command, "start") == 0) {
        // 检查是否指定了配置文件
        if (argc > 3 && strcmp(argv[2], "-c") == 0) {
            config_file = argv[3];
        }

        printf("Starting FIM blacklist protection...\n");
        printf("Config file: %s\n\n", config_file);

        // 解析YAML配置
        if (parse_yaml_config(config_file) != 0) {
            fprintf(stderr, "Failed to parse config file\n");
            return 1;
        }

        // 加载eBPF程序
        if (load_ebpf_program() != 0) {
            fprintf(stderr, "Failed to load eBPF program\n");
            return 1;
        }

        // 应用黑名单配置
        if (apply_blacklist_config() == 0) {
            fprintf(stderr, "No blacklist entries to apply\n");
            return 1;
        }

        printf("\n🛡️  FIM protection is now active!\n");
        printf("📋 Check blocked access with: sudo cat /sys/kernel/debug/tracing/trace_pipe\n");
        printf("Press Ctrl+C to stop...\n\n");

        // 保持程序运行
        while (running) {
            sleep(1);
        }

        cleanup();

    } else if (strcmp(command, "load") == 0) {
        if (argc < 3) {
            fprintf(stderr, "Error: config file required for load command\n");
            return 1;
        }

        config_file = argv[2];
        printf("Loading blacklist configuration from: %s\n", config_file);

        if (parse_yaml_config(config_file) != 0) {
            fprintf(stderr, "Failed to parse config file\n");
            return 1;
        }

        printf("Configuration loaded successfully.\n");

    } else if (strcmp(command, "status") == 0) {
        printf("FIM Blacklist Status:\n");
        printf("- eBPF LSM: %s\n", access("/sys/kernel/security/lsm", R_OK) == 0 ? "Available" : "Not available");
        printf("- BPF programs: %s\n", obj ? "Loaded" : "Not loaded");
        printf("- Default config: %s\n", config_file);

    } else {
        fprintf(stderr, "Unknown command: %s\n", command);
        print_usage(argv[0]);
        return 1;
    }

    return 0;
}