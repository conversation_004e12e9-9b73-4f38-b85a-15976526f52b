#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>

#define EACCES 13

char LICENSE[] SEC("license") = "GPL";

/* 白名单map：存储允许访问的inode */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u32);
    __type(value, __u32);
    __uint(max_entries, 65536);
    __uint(pinning, LIBBPF_PIN_BY_NAME);
} whitemap SEC(".maps");

/* 目标目录inode - 需要被拦截的目录 */
#define TARGET_INODE 268136

/* 检查是否是目标目录或其子目录 */
static __always_inline int is_target_or_child(struct dentry *de) {
    struct dentry *cur = de;

    for (int i = 0; i < 6; i++) {
        if (!cur) break;

        struct inode *inode = BPF_CORE_READ(cur, d_inode);
        if (!inode) break;

        __u32 ino = BPF_CORE_READ(inode, i_ino);
        if (ino == TARGET_INODE) return 1;  // 找到目标目录

        struct dentry *parent = BPF_CORE_READ(cur, d_parent);
        if (parent == cur) break;  // 到达根目录
        cur = parent;
    }
    return 0;
}

/* 检查当前dentry或其父目录是否在白名单中 */
static __always_inline int allow_inode_or_parents(struct dentry *de) {
    // 首先检查是否是目标目录或其子目录
    if (is_target_or_child(de)) return 0;  // 不允许访问

    struct dentry *cur = de;

    for (int i = 0; i < 6; i++) {
        if (!cur) break;

        struct inode *inode = BPF_CORE_READ(cur, d_inode);
        if (!inode) break;

        __u32 ino = BPF_CORE_READ(inode, i_ino);
        __u32 *found = bpf_map_lookup_elem(&whitemap, &ino);
        if (found) return 1;

        struct dentry *parent = BPF_CORE_READ(cur, d_parent);
        if (parent == cur) break;  // 到达根目录
        cur = parent;
    }
    return 0;
}

/* 文件模式常量 */
#define FMODE_READ      0x1
#define FMODE_WRITE     0x2

/* LSM Hook: 文件打开 */
SEC("lsm/file_open")
int BPF_PROG(block_file_open, struct file *file) {
    struct dentry *dentry = BPF_CORE_READ(file, f_path.dentry);
    if (!dentry) return 0;

    // 检查文件打开模式
    __u32 f_mode = BPF_CORE_READ(file, f_mode);

    // 如果是读操作或写操作，都进行拦截检查
    if (f_mode & (FMODE_READ | FMODE_WRITE)) {
        if (allow_inode_or_parents(dentry)) return 0;

        if (f_mode & FMODE_WRITE) {
            bpf_printk("FIM: Blocked file_open WRITE access\n");
        } else {
            bpf_printk("FIM: Blocked file_open READ access\n");
        }
        return -EACCES;
    }

    return 0;
}

/* LSM Hook: inode权限检查 */
SEC("lsm/inode_permission")
int BPF_PROG(block_inode_permission, struct inode *inode, int mask) {
    if (!inode) return 0;

    // 构造一个临时的dentry来检查权限
    // 注意：这里我们只能检查inode本身，无法递归检查父目录
    __u32 ino = BPF_CORE_READ(inode, i_ino);
    __u32 *found = bpf_map_lookup_elem(&whitemap, &ino);
    if (found) return 0;

    bpf_printk("FIM: Blocked inode_permission access to inode %u\n", ino);
    return -EACCES;
}

/* LSM Hook: 目录读取 */
SEC("lsm/file_permission")
int BPF_PROG(block_file_permission, struct file *file, int mask) {
    struct dentry *dentry = BPF_CORE_READ(file, f_path.dentry);
    if (!dentry) return 0;

    if (allow_inode_or_parents(dentry)) return 0;

    bpf_printk("FIM: Blocked file_permission access\n");
    return -EACCES;
}

/* LSM Hook: 文件创建 */
SEC("lsm/inode_create")
int BPF_PROG(block_inode_create, struct inode *dir, struct dentry *dentry, umode_t mode) {
    if (!dentry) return 0;

    if (allow_inode_or_parents(dentry)) return 0;

    bpf_printk("FIM: Blocked inode_create access\n");
    return -EACCES;
}

/* LSM Hook: 文件删除 */
SEC("lsm/inode_unlink")
int BPF_PROG(block_inode_unlink, struct inode *dir, struct dentry *dentry) {
    if (!dentry) return 0;

    if (allow_inode_or_parents(dentry)) return 0;

    bpf_printk("FIM: Blocked inode_unlink access\n");
    return -EACCES;
}

/* LSM Hook: 目录删除 */
SEC("lsm/inode_rmdir")
int BPF_PROG(block_inode_rmdir, struct inode *dir, struct dentry *dentry) {
    if (!dentry) return 0;

    if (allow_inode_or_parents(dentry)) return 0;

    bpf_printk("FIM: Blocked inode_rmdir access\n");
    return -EACCES;
}

/* LSM Hook: 文件重命名 */
SEC("lsm/inode_rename")
int BPF_PROG(block_inode_rename, struct inode *old_dir, struct dentry *old_dentry,
             struct inode *new_dir, struct dentry *new_dentry) {
    if (!old_dentry || !new_dentry) return 0;

    // 检查源路径
    if (!allow_inode_or_parents(old_dentry)) {
        bpf_printk("FIM: Blocked inode_rename from source\n");
        return -EACCES;
    }

    // 检查目标路径
    if (!allow_inode_or_parents(new_dentry)) {
        bpf_printk("FIM: Blocked inode_rename to target\n");
        return -EACCES;
    }

    return 0;
}