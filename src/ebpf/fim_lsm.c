#include "fim_common.h"

char LICENSE[] SEC("license") = "GPL";

/* 检查访问权限：黑名单或白名单模式 */
static __always_inline int should_block_access(struct dentry *de) {
    struct dentry *cur = de;
    int found_in_blacklist = 0;
    int found_in_whitelist = 0;

    for (int i = 0; i < 6; i++) {
        if (!cur) break;

        struct inode *inode = BPF_CORE_READ(cur, d_inode);
        if (!inode) break;

        __u32 ino = BPF_CORE_READ(inode, i_ino);

        // 检查黑名单
        __u32 *blacklist_entry = bpf_map_lookup_elem(&blacklist_map, &ino);
        if (blacklist_entry) {
            found_in_blacklist = 1;
        }

        // 检查白名单
        __u32 *whitelist_entry = bpf_map_lookup_elem(&whitelist_map, &ino);
        if (whitelist_entry) {
            found_in_whitelist = 1;
        }

        struct dentry *parent = BPF_CORE_READ(cur, d_parent);
        if (parent == cur) break;  // 到达根目录
        cur = parent;
    }

    // 决策逻辑：
    // 1. 如果在黑名单中 → 拦截
    // 2. 如果有白名单条目且不在白名单中 → 拦截
    // 3. 其他情况 → 允许

    if (found_in_blacklist) {
        return 1;  // 黑名单拦截
    }

    // 检查是否启用了白名单模式（有白名单条目）
    __u32 key = 0;
    __u32 *first_whitelist = bpf_map_lookup_elem(&whitelist_map, &key);
    if (first_whitelist) {  // 有白名单条目，启用白名单模式
        if (!found_in_whitelist) {
            return 1;  // 不在白名单中，拦截
        }
    }

    return 0;  // 允许访问
}

/* LSM Hook: 文件打开权限检查 */
SEC("lsm/file_open")
int BPF_PROG(fim_file_open, struct file *file) {
    struct dentry *dentry = BPF_CORE_READ(file, f_path.dentry);
    if (!dentry) return 0;

    if (should_block_access(dentry)) {
        bpf_printk("FIM: Blocked file_open access\n");
        return -EACCES;
    }

    return 0;
}


/* LSM Hook: inode创建 */
SEC("lsm/inode_create")
int BPF_PROG(fim_inode_create, struct inode *dir, struct dentry *dentry, umode_t mode) {
    if (!dentry) return 0;

    if (should_block_access(dentry)) {
        bpf_printk("FIM: Blocked inode_create access\n");
        return -EACCES;
    }

    return 0;
}

/* LSM Hook: inode删除 */
SEC("lsm/inode_unlink")
int BPF_PROG(fim_inode_unlink, struct inode *dir, struct dentry *dentry) {
    if (!dentry) return 0;

    if (should_block_access(dentry)) {
        bpf_printk("FIM: Blocked inode_unlink access\n");
        return -EACCES;
    }

    return 0;
}

/* LSM Hook: 文件重命名 */
SEC("lsm/inode_rename")
int BPF_PROG(fim_inode_rename, struct inode *old_dir, struct dentry *old_dentry,
             struct inode *new_dir, struct dentry *new_dentry) {
    if (!old_dentry || !new_dentry) return 0;

    // 检查源路径
    if (should_block_access(old_dentry)) {
        bpf_printk("FIM: Blocked inode_rename from source\n");
        return -EACCES;
    }

    // 检查目标路径
    if (should_block_access(new_dentry)) {
        bpf_printk("FIM: Blocked inode_rename to target\n");
        return -EACCES;
    }

    return 0;
}