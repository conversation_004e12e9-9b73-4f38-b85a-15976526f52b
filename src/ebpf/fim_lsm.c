#include "fim_common.h"

char LICENSE[] SEC("license") = "GPL";

/* 检查文件访问权限 */
static __always_inline int check_file_access(struct dentry *de, __u32 operation) {
    struct dentry *cur = de;

    for (int i = 0; i < 6; i++) {
        if (!cur) break;

        struct inode *inode = BPF_CORE_READ(cur, d_inode);
        if (!inode) break;

        __u32 ino = BPF_CORE_READ(inode, i_ino);
        struct fim_rule *rule = NULL;

        // 根据操作类型选择对应的规则map
        if (operation == FIM_OP_READ) {
            rule = bpf_map_lookup_elem(&read_rules_map, &ino);
        } else if (operation == FIM_OP_WRITE) {
            rule = bpf_map_lookup_elem(&write_rules_map, &ino);
        }

        if (rule && rule->enabled) {
            // 找到匹配的规则
            if (rule->rule_type == FIM_RULE_DENY) {
                // 拒绝规则：拦截访问
                if (rule->action == FIM_ACTION_BLOCK) {
                    bpf_printk("FIM: Blocked by deny rule %s\n", rule->rule_id);
                    return 1;  // 拦截
                } else if (rule->action == FIM_ACTION_AUDIT) {
                    bpf_printk("FIM: Audit deny rule %s\n", rule->rule_id);
                    // 审计但不拦截
                }
            } else if (rule->rule_type == FIM_RULE_ALLOW) {
                // 允许规则：明确允许
                if (rule->action == FIM_ACTION_AUDIT) {
                    bpf_printk("FIM: Audit allow rule %s\n", rule->rule_id);
                }
                return 0;  // 允许
            }
        }

        struct dentry *parent = BPF_CORE_READ(cur, d_parent);
        if (parent == cur) break;  // 到达根目录
        cur = parent;
    }

    return 0;  // 默认允许
}

/* LSM Hook: 文件打开权限检查 */
SEC("lsm/file_open")
int BPF_PROG(fim_file_open, struct file *file) {
    struct dentry *dentry = BPF_CORE_READ(file, f_path.dentry);
    if (!dentry) return 0;

    // 获取文件打开模式
    __u32 f_mode = BPF_CORE_READ(file, f_mode);
    __u32 operation;

    if (f_mode & FMODE_WRITE) {
        operation = FIM_OP_WRITE;
    } else {
        operation = FIM_OP_READ;
    }

    if (check_file_access(dentry, operation)) {
        return -EACCES;
    }

    return 0;
}


/* LSM Hook: inode创建 */
SEC("lsm/inode_create")
int BPF_PROG(fim_inode_create, struct inode *dir, struct dentry *dentry, umode_t mode) {
    if (!dentry) return 0;

    if (check_file_access(dentry, FIM_OP_WRITE)) {
        return -EACCES;
    }

    return 0;
}

/* LSM Hook: inode删除 */
SEC("lsm/inode_unlink")
int BPF_PROG(fim_inode_unlink, struct inode *dir, struct dentry *dentry) {
    if (!dentry) return 0;

    if (check_file_access(dentry, FIM_OP_WRITE)) {
        return -EACCES;
    }

    return 0;
}

/* LSM Hook: 文件重命名 */
SEC("lsm/inode_rename")
int BPF_PROG(fim_inode_rename, struct inode *old_dir, struct dentry *old_dentry,
             struct inode *new_dir, struct dentry *new_dentry) {
    if (!old_dentry || !new_dentry) return 0;

    // 检查源路径
    if (check_file_access(old_dentry, FIM_OP_WRITE)) {
        return -EACCES;
    }

    // 检查目标路径
    if (check_file_access(new_dentry, FIM_OP_WRITE)) {
        return -EACCES;
    }

    return 0;
}