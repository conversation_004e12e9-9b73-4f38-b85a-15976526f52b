#include "fim_common.h"

char LICENSE[] SEC("license") = "GPL";

/* LSM Hook: 文件打开权限检查 - 区分读写操作 */
SEC("lsm/file_open")
int BPF_PROG(fim_file_open, struct file *file)
{
    struct fim_config *config;
    struct dentry *dentry;
    struct inode *inode;
    char path[FIM_MAX_PATH_LEN];
    __u32 operation;
    __u32 decision = FIM_DENY;
    int whitelist_hit = 0;
    int path_len;
    __u32 f_mode;

    /* 获取配置 */
    config = get_config();
    if (!config || !config->enabled)
        return 0;

    /* 获取dentry和inode */
    dentry = BPF_CORE_READ(file, f_path.dentry);
    if (!dentry)
        return 0;

    inode = BPF_CORE_READ(dentry, d_inode);
    if (!inode)
        return 0;

    /* 获取文件打开模式 */
    f_mode = BPF_CORE_READ(file, f_mode);

    /* 根据文件打开模式确定操作类型 */
    if (f_mode & FMODE_WRITE) {
        /* 写模式：包括 O_WRONLY, O_RDWR, O_APPEND 等 */
        operation = FIM_OP_OPEN_WRITE;
    } else if (f_mode & FMODE_READ) {
        /* 只读模式：O_RDONLY */
        operation = FIM_OP_OPEN_READ;
    } else {
        /* 其他模式，默认按读处理 */
        operation = FIM_OP_OPEN_READ;
    }

    /* 根据配置的模式做出访问决策 */
    decision = make_access_decision(dentry, operation, config);
    __u32 rule_type = 0;
    whitelist_hit = check_policy_or_parents(dentry, operation, &rule_type);

    /* 获取路径用于日志 */
    path_len = get_file_path(file, path, sizeof(path));

    /* 记录事件和统计 */
    if (config->log_level >= FIM_LOG_INFO) {
        log_event(operation, decision, inode, (path_len > 0) ? path : NULL);
    }
    update_stats(decision, whitelist_hit);

    /* 返回决策 */
    return (decision == FIM_ALLOW) ? 0 : -EACCES;
}



/* LSM Hook: inode创建 */
SEC("lsm/inode_create")
int BPF_PROG(fim_inode_create, struct inode *dir, struct dentry *dentry, umode_t mode)
{
    struct fim_config *config;
    struct dentry *parent_dentry;
    char path[FIM_MAX_PATH_LEN];
    __u32 decision = FIM_DENY;
    int whitelist_hit = 0;

    config = get_config();
    if (!config || !config->enabled)
        return 0;

    /* 检查父目录权限 - 在父目录中创建文件需要父目录的创建权限 */
    parent_dentry = BPF_CORE_READ(dentry, d_parent);
    if (parent_dentry) {
        decision = make_access_decision(parent_dentry, FIM_OP_CREATE, config);
        __u32 rule_type = 0;
        whitelist_hit = check_policy_or_parents(parent_dentry, FIM_OP_CREATE, &rule_type);
    } else {
        decision = config->default_action;
        whitelist_hit = 0;
    }

    /* 构建路径用于日志 */
    struct qstr d_name;
    const char *name;
    int name_len;

    BPF_CORE_READ_INTO(&d_name, dentry, d_name);
    name = BPF_CORE_READ(d_name, name);
    name_len = BPF_CORE_READ(d_name, len);

    if (name_len > 0 && name_len < FIM_MAX_FILENAME) {
        bpf_probe_read_kernel_str(path, sizeof(path), name);
    } else {
        path[0] = '\0';
    }

    if (config->log_level >= FIM_LOG_INFO) {
        log_event(FIM_OP_CREATE, decision, dir, path);
    }
    update_stats(decision, whitelist_hit);

    return (decision == FIM_ALLOW) ? 0 : -EACCES;
}

/* LSM Hook: inode删除 */
SEC("lsm/inode_unlink")
int BPF_PROG(fim_inode_unlink, struct inode *dir, struct dentry *dentry)
{
    struct fim_config *config;
    struct inode *target_inode;
    char path[FIM_MAX_PATH_LEN];
    __u32 decision = FIM_DENY;
    int whitelist_hit = 0;

    config = get_config();
    if (!config || !config->enabled)
        return 0;

    target_inode = BPF_CORE_READ(dentry, d_inode);

    /* 根据配置的模式检查删除权限 */
    decision = make_access_decision(dentry, FIM_OP_DELETE, config);
    __u32 rule_type = 0;
    whitelist_hit = check_policy_or_parents(dentry, FIM_OP_DELETE, &rule_type);

    /* 构建路径用于日志 */
    struct qstr d_name;
    const char *name;
    int name_len;

    BPF_CORE_READ_INTO(&d_name, dentry, d_name);
    name = BPF_CORE_READ(d_name, name);
    name_len = BPF_CORE_READ(d_name, len);

    if (name_len > 0 && name_len < FIM_MAX_FILENAME) {
        bpf_probe_read_kernel_str(path, sizeof(path), name);
    } else {
        path[0] = '\0';
    }

    if (config->log_level >= FIM_LOG_INFO) {
        log_event(FIM_OP_DELETE, decision, target_inode, path);
    }
    update_stats(decision, whitelist_hit);

    return (decision == FIM_ALLOW) ? 0 : -EACCES;
}

/* LSM Hook: 文件重命名 */
SEC("lsm/inode_rename")
int BPF_PROG(fim_inode_rename, struct inode *old_dir, struct dentry *old_dentry,
             struct inode *new_dir, struct dentry *new_dentry)
{
    struct fim_config *config;
    char old_path[FIM_MAX_PATH_LEN];
    char new_path[FIM_MAX_PATH_LEN];
    __u32 decision = FIM_DENY;
    int whitelist_hit = 0;
    
    config = get_config();
    if (!config || !config->enabled)
        return 0;
    
    /* 检查源文件和目标文件路径 */
    struct qstr old_name, new_name;
    
    BPF_CORE_READ_INTO(&old_name, old_dentry, d_name);
    BPF_CORE_READ_INTO(&new_name, new_dentry, d_name);
    
    bpf_probe_read_kernel_str(old_path, sizeof(old_path), BPF_CORE_READ(old_name, name));
    bpf_probe_read_kernel_str(new_path, sizeof(new_path), BPF_CORE_READ(new_name, name));
    
    /* 检查重命名权限 - 需要检查源文件和目标位置 */
    int old_decision = make_access_decision(old_dentry, FIM_OP_RENAME, config);
    int new_decision = make_access_decision(new_dentry, FIM_OP_RENAME, config);

    /* 两个位置都需要允许重命名操作 */
    if (old_decision == FIM_ALLOW && new_decision == FIM_ALLOW) {
        decision = FIM_ALLOW;
        whitelist_hit = 1;
    } else {
        decision = FIM_DENY;
        __u32 rule_type1 = 0, rule_type2 = 0;
        whitelist_hit = (check_policy_or_parents(old_dentry, FIM_OP_RENAME, &rule_type1) ||
                        check_policy_or_parents(new_dentry, FIM_OP_RENAME, &rule_type2));
    }
    
    if (config->log_level >= FIM_LOG_INFO) {
        struct inode *old_inode = BPF_CORE_READ(old_dentry, d_inode);
        log_event(FIM_OP_RENAME, decision, old_inode, old_path);
    }
    update_stats(decision, whitelist_hit);
    
    return (decision == FIM_ALLOW) ? 0 : -EACCES;
}

/* LSM Hook: 文件属性修改 */
SEC("lsm/inode_setattr")
int BPF_PROG(fim_inode_setattr, struct dentry *dentry, struct iattr *attr)
{
    struct fim_config *config;
    char path[FIM_MAX_PATH_LEN];
    __u32 operation;
    __u32 decision = FIM_DENY;
    int whitelist_hit = 0;
    
    config = get_config();
    if (!config || !config->enabled)
        return 0;
    
    /* 确定属性修改类型 */
    if (attr->ia_valid & ATTR_MODE) {
        operation = FIM_OP_CHMOD;
    } else if (attr->ia_valid & (ATTR_UID | ATTR_GID)) {
        operation = FIM_OP_CHOWN;
    } else if (attr->ia_valid & ATTR_SIZE) {
        operation = FIM_OP_TRUNCATE;
    } else {
        return 0; /* 其他属性修改不拦截 */
    }
    
    /* 根据配置的模式检查属性修改权限 */
    decision = make_access_decision(dentry, operation, config);
    __u32 rule_type = 0;
    whitelist_hit = check_policy_or_parents(dentry, operation, &rule_type);

    /* 构建路径用于日志 */
    struct qstr d_name;
    BPF_CORE_READ_INTO(&d_name, dentry, d_name);
    bpf_probe_read_kernel_str(path, sizeof(path), BPF_CORE_READ(d_name, name));

    if (config->log_level >= FIM_LOG_INFO) {
        struct inode *inode = BPF_CORE_READ(dentry, d_inode);
        log_event(operation, decision, inode, path);
    }
    update_stats(decision, whitelist_hit);

    return (decision == FIM_ALLOW) ? 0 : -EACCES;
}
