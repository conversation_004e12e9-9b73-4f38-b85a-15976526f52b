#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#define EACCES 13

char LICENSE[] SEC("license") = "GPL";

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u32);
    __type(value, __u32);
    __uint(max_entries, 65536);
    __uint(pinning, LIBBPF_PIN_BY_NAME);
} whitemap SEC(".maps");

static __always_inline int allow_inode_or_parents(struct dentry *de) {
    struct dentry *cur = de;
    for (int i = 0; i < 6; i++) {
        if (!cur || !cur->d_inode) break;
        __u32 inode = cur->d_inode->i_ino;
        __u32 *found = bpf_map_lookup_elem(&whitemap, &inode);
        if (found) return 1;
        if (cur == cur->d_parent) break;
        cur = cur->d_parent;
    }
    return 0;
}

SEC("lsm/file_open")
int block_open(struct file *file) {
    struct dentry *de = file->f_path.dentry;
    if (!de || !de->d_inode) return 0;
    if (allow_inode_or_parents(de)) return 0;
    return -EACCES;
}



SEC("lsm/inode_create")
int block_create(struct inode *dir, struct dentry *dentry, umode_t mode) {
    if (!dentry) return 0;
    if (allow_inode_or_parents(dentry)) return 0;
    return -EACCES;
}

SEC("lsm/inode_unlink")
int block_unlink(struct inode *dir, struct dentry *dentry) {
    if (!dentry) return 0;
    if (allow_inode_or_parents(dentry)) return 0;
    return -EACCES;
}

SEC("lsm/inode_rename")
int block_rename(struct inode *old_dir, struct dentry *old_dentry,
                 struct inode *new_dir, struct dentry *new_dentry) {
    if (!old_dentry || !new_dentry) return 0;
    if (allow_inode_or_parents(old_dentry) && allow_inode_or_parents(new_dentry)) return 0;
    return -EACCES;
}


