#include "fim_common.h"

char LICENSE[] SEC("license") = "GPL";

/* 检查文件访问权限 */
static __always_inline int check_file_access(struct dentry *de, __u32 operation) {
    struct dentry *cur = de;
    int found_allow = 0;

    for (int i = 0; i < 6; i++) {
        if (!cur) break;

        struct inode *inode = BPF_CORE_READ(cur, d_inode);
        if (!inode) {
            // 如果当前dentry没有inode（如inode_create情况），检查父目录
            struct dentry *parent = BPF_CORE_READ(cur, d_parent);
            if (parent == cur) break;  // 到达根目录
            cur = parent;
            continue;
        }

        __u32 ino = BPF_CORE_READ(inode, i_ino);
        struct fim_rule *deny_rule = NULL;
        struct fim_rule *allow_rule = NULL;

        // 根据操作类型检查对应的规则map
        if (operation == FIM_OP_READ) {
            deny_rule = bpf_map_lookup_elem(&read_deny_map, &ino);
            allow_rule = bpf_map_lookup_elem(&read_allow_map, &ino);
        } else if (operation == FIM_OP_WRITE) {
            deny_rule = bpf_map_lookup_elem(&write_deny_map, &ino);
            allow_rule = bpf_map_lookup_elem(&write_allow_map, &ino);
        }

        // 检查拒绝规则
        if (deny_rule && deny_rule->enabled) {
            bpf_printk("FIM: Blocked by deny rule\n");
            return 1;  // 拦截
        }

        // 检查允许规则
        if (allow_rule && allow_rule->enabled) {
            found_allow = 1;
        }

        struct dentry *parent = BPF_CORE_READ(cur, d_parent);
        if (parent == cur) break;  // 到达根目录
        cur = parent;
    }

    // 如果有allow_list规则，但当前路径不在allow_list中，则拦截
    // 检查是否有任何allow规则存在
    __u32 test_key = 0;
    struct fim_rule *test_allow = NULL;
    if (operation == FIM_OP_READ) {
        test_allow = bpf_map_lookup_elem(&read_allow_map, &test_key);
    } else if (operation == FIM_OP_WRITE) {
        test_allow = bpf_map_lookup_elem(&write_allow_map, &test_key);
    }

    if (test_allow && !found_allow) {
        bpf_printk("FIM: Blocked - not in allow list\n");
        return 1;  // 不在白名单中，拦截
    }

    return 0;  // 默认允许
}

/* LSM Hook: 文件打开权限检查 */
SEC("lsm/file_open")
int BPF_PROG(fim_file_open, struct file *file) {
    struct dentry *dentry = BPF_CORE_READ(file, f_path.dentry);
    if (!dentry) return 0;

    // 获取文件打开模式
    __u32 f_mode = BPF_CORE_READ(file, f_mode);
    __u32 operation;

    if (f_mode & FMODE_WRITE) {
        operation = FIM_OP_WRITE;
    } else {
        operation = FIM_OP_READ;
    }

    if (check_file_access(dentry, operation)) {
        return -EACCES;
    }

    return 0;
}


/* LSM Hook: inode创建 */
SEC("lsm/inode_create")
int BPF_PROG(fim_inode_create, struct inode *dir, struct dentry *dentry, umode_t mode) {
    if (!dentry || !dir) return 0;

    // 对于创建操作，新文件的dentry还没有inode
    // 直接检查父目录inode
    __u32 parent_ino = BPF_CORE_READ(dir, i_ino);
    struct fim_rule *deny_rule = bpf_map_lookup_elem(&write_deny_map, &parent_ino);
    if (deny_rule && deny_rule->enabled) {
        bpf_printk("FIM: Blocked inode_create - parent dir in deny list\n");
        return -EACCES;
    }

    return 0;
}

/* LSM Hook: inode删除 */
SEC("lsm/inode_unlink")
int BPF_PROG(fim_inode_unlink, struct inode *dir, struct dentry *dentry) {
    if (!dentry) return 0;

    if (check_file_access(dentry, FIM_OP_WRITE)) {
        return -EACCES;
    }

    return 0;
}

/* LSM Hook: 文件重命名 */
SEC("lsm/inode_rename")
int BPF_PROG(fim_inode_rename, struct inode *old_dir, struct dentry *old_dentry,
             struct inode *new_dir, struct dentry *new_dentry) {
    if (!old_dentry || !new_dentry) return 0;

    // 检查源路径
    if (check_file_access(old_dentry, FIM_OP_WRITE)) {
        return -EACCES;
    }

    // 检查目标路径
    if (check_file_access(new_dentry, FIM_OP_WRITE)) {
        return -EACCES;
    }

    return 0;
}

/* LSM Hook: 目录创建 */
SEC("lsm/inode_mkdir")
int BPF_PROG(fim_inode_mkdir, struct inode *dir, struct dentry *dentry, umode_t mode) {
    if (!dentry || !dir) return 0;

    // 直接检查父目录inode
    __u32 parent_ino = BPF_CORE_READ(dir, i_ino);
    struct fim_rule *deny_rule = bpf_map_lookup_elem(&write_deny_map, &parent_ino);
    if (deny_rule && deny_rule->enabled) {
        bpf_printk("FIM: Blocked inode_mkdir - parent dir in deny list\n");
        return -EACCES;
    }

    return 0;
}

/* LSM Hook: 目录删除 */
SEC("lsm/inode_rmdir")
int BPF_PROG(fim_inode_rmdir, struct inode *dir, struct dentry *dentry) {
    if (!dentry) return 0;

    if (check_file_access(dentry, FIM_OP_WRITE)) {
        return -EACCES;
    }

    return 0;
}

/* LSM Hook: 文件权限修改 */
SEC("lsm/inode_setattr")
int BPF_PROG(fim_inode_setattr, struct dentry *dentry, struct iattr *attr) {
    if (!dentry) return 0;

    if (check_file_access(dentry, FIM_OP_WRITE)) {
        return -EACCES;
    }

    return 0;
}

/* LSM Hook: 硬链接创建 */
SEC("lsm/inode_link")
int BPF_PROG(fim_inode_link, struct dentry *old_dentry, struct inode *dir, struct dentry *new_dentry) {
    if (!new_dentry || !dir) return 0;

    // 直接检查父目录inode
    __u32 parent_ino = BPF_CORE_READ(dir, i_ino);
    struct fim_rule *deny_rule = bpf_map_lookup_elem(&write_deny_map, &parent_ino);
    if (deny_rule && deny_rule->enabled) {
        bpf_printk("FIM: Blocked inode_link - parent dir in deny list\n");
        return -EACCES;
    }

    return 0;
}

/* LSM Hook: 符号链接创建 */
SEC("lsm/inode_symlink")
int BPF_PROG(fim_inode_symlink, struct inode *dir, struct dentry *dentry, const char *old_name) {
    if (!dentry || !dir) return 0;

    // 直接检查父目录inode
    __u32 parent_ino = BPF_CORE_READ(dir, i_ino);
    struct fim_rule *deny_rule = bpf_map_lookup_elem(&write_deny_map, &parent_ino);
    if (deny_rule && deny_rule->enabled) {
        bpf_printk("FIM: Blocked inode_symlink - parent dir in deny list\n");
        return -EACCES;
    }

    return 0;
}