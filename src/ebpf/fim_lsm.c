#include "fim_common.h"

char LICENSE[] SEC("license") = "GPL";



/* LSM Hook: 文件打开权限检查 */
SEC("lsm/file_open")
int fim_file_open(struct file *file) {
    struct fim_config *config;
    struct dentry *dentry;
    struct inode *inode;
    __u32 operation;
    __u32 decision = FIM_DENY;
    int policy_hit = 0;
    __u32 f_mode;

    /* 获取配置 */
    config = get_config();
    if (!config || !config->enabled)
        return 0;

    /* 获取dentry和inode */
    dentry = BPF_CORE_READ(file, f_path.dentry);
    if (!dentry)
        return 0;

    inode = BPF_CORE_READ(dentry, d_inode);
    if (!inode)
        return 0;

    /* 获取文件打开模式 */
    f_mode = BPF_CORE_READ(file, f_mode);

    /* 根据文件打开模式确定操作类型 */
    if (f_mode & FMODE_WRITE) {
        operation = FIM_OP_OPEN_WRITE;
    } else {
        operation = FIM_OP_OPEN_READ;
    }

    /* 检查策略 */
    int policy_result = check_policy_or_parents(dentry, operation);
    if (policy_result >= 0) {
        decision = policy_result;
        policy_hit = 1;
    } else {
        decision = config ? config->default_action : FIM_DENY;
        policy_hit = 0;
    }

    /* 记录事件和统计 */
    if (config && config->log_level >= FIM_LOG_INFO) {
        log_event(operation, decision, inode, NULL);
    }
    update_stats(decision, policy_hit);

    /* 返回决策 */
    return (decision == FIM_ALLOW) ? 0 : -EACCES;
}


/* LSM Hook: inode创建 */
SEC("lsm/inode_create")
int fim_inode_create(struct inode *dir, struct dentry *dentry, umode_t mode) {
    struct fim_config *config;
    __u32 decision = FIM_DENY;
    int policy_hit = 0;

    config = get_config();
    if (!config || !config->enabled)
        return 0;

    /* 检查父目录的创建权限 */
    struct dentry *parent_dentry = BPF_CORE_READ(dentry, d_parent);
    if (parent_dentry) {
        int policy_result = check_policy_or_parents(parent_dentry, FIM_OP_CREATE);
        if (policy_result >= 0) {
            decision = policy_result;
            policy_hit = 1;
        } else {
            decision = config ? config->default_action : FIM_DENY;
            policy_hit = 0;
        }
    } else {
        decision = config ? config->default_action : FIM_DENY;
        policy_hit = 0;
    }

    if (config && config->log_level >= FIM_LOG_INFO) {
        log_event(FIM_OP_CREATE, decision, dir, NULL);
    }
    update_stats(decision, policy_hit);

    return (decision == FIM_ALLOW) ? 0 : -EACCES;
}

/* LSM Hook: inode删除 */
SEC("lsm/inode_unlink")
int fim_inode_unlink(struct inode *dir, struct dentry *dentry) {
    struct fim_config *config;
    __u32 decision = FIM_DENY;
    int policy_hit = 0;

    config = get_config();
    if (!config || !config->enabled)
        return 0;

    /* 检查删除权限 */
    int policy_result = check_policy_or_parents(dentry, FIM_OP_DELETE);
    if (policy_result >= 0) {
        decision = policy_result;
        policy_hit = 1;
    } else {
        decision = config ? config->default_action : FIM_DENY;
        policy_hit = 0;
    }

    if (config && config->log_level >= FIM_LOG_INFO) {
        struct inode *target_inode = BPF_CORE_READ(dentry, d_inode);
        log_event(FIM_OP_DELETE, decision, target_inode, NULL);
    }
    update_stats(decision, policy_hit);

    return (decision == FIM_ALLOW) ? 0 : -EACCES;
}

/* LSM Hook: 文件重命名 */
SEC("lsm/inode_rename")
int fim_inode_rename(struct inode *old_dir, struct dentry *old_dentry,
                     struct inode *new_dir, struct dentry *new_dentry) {
    struct fim_config *config;
    __u32 decision = FIM_DENY;
    int policy_hit = 0;

    config = get_config();
    if (!config || !config->enabled)
        return 0;

    /* 检查重命名权限 - 需要检查源文件和目标位置 */
    int old_result = check_policy_or_parents(old_dentry, FIM_OP_RENAME);
    int new_result = check_policy_or_parents(new_dentry, FIM_OP_RENAME);

    /* 两个位置都需要允许重命名操作 */
    if (old_result == FIM_ALLOW && new_result == FIM_ALLOW) {
        decision = FIM_ALLOW;
        policy_hit = 1;
    } else if (old_result == FIM_DENY || new_result == FIM_DENY) {
        decision = FIM_DENY;
        policy_hit = 1;
    } else {
        decision = config ? config->default_action : FIM_DENY;
        policy_hit = 0;
    }

    if (config && config->log_level >= FIM_LOG_INFO) {
        struct inode *old_inode = BPF_CORE_READ(old_dentry, d_inode);
        log_event(FIM_OP_RENAME, decision, old_inode, NULL);
    }
    update_stats(decision, policy_hit);

    return (decision == FIM_ALLOW) ? 0 : -EACCES;
}