#ifndef __FIM_EBPF_COMMON_H__
#define __FIM_EBPF_COMMON_H__

#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>

/* 错误码 */
#define EACCES 13

/* 黑名单map：存储需要拦截的目录inode */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u32);
    __type(value, __u32);
    __uint(max_entries, 1024);
    __uint(pinning, LIBBPF_PIN_BY_NAME);
} blacklist_map SEC(".maps");

#endif /* __FIM_EBPF_COMMON_H__ */