#ifndef __FIM_EBPF_COMMON_H__
#define __FIM_EBPF_COMMON_H__

#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>

/* 错误码 */
#define EACCES 13

/* 操作类型 */
#define FIM_OP_READ  1
#define FIM_OP_WRITE 2

/* 动作类型 */
#define FIM_ACTION_BLOCK 1
#define FIM_ACTION_AUDIT 2

/* 规则类型 */
#define FIM_RULE_DENY  1
#define FIM_RULE_ALLOW 2

/* 文件模式常量 */
#define FMODE_READ      0x1
#define FMODE_WRITE     0x2

/* 规则条目结构 */
struct fim_rule {
    __u32 inode;           // 文件/目录inode
    __u32 operation;       // 操作类型：读/写
    __u32 rule_type;       // 规则类型：允许/拒绝
    __u32 action;          // 动作：拦截/审计
    __u32 is_dir;          // 是否为目录
    __u32 enabled;         // 是否启用
    char rule_id[32];      // 规则ID
};

/* 读操作规则map */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u32);
    __type(value, struct fim_rule);
    __uint(max_entries, 1024);
    __uint(pinning, LIBBPF_PIN_BY_NAME);
} read_rules_map SEC(".maps");

/* 写操作规则map */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u32);
    __type(value, struct fim_rule);
    __uint(max_entries, 1024);
    __uint(pinning, LIBBPF_PIN_BY_NAME);
} write_rules_map SEC(".maps");

#endif /* __FIM_EBPF_COMMON_H__ */