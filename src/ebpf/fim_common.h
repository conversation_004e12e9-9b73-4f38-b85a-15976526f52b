#ifndef __FIM_EBPF_COMMON_H__
#define __FIM_EBPF_COMMON_H__

#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>

/* 错误码 */
#define EACCES 13

/* 操作类型 */
#define FIM_OP_READ  1
#define FIM_OP_WRITE 2

/* 规则类型 */
#define FIM_RULE_DENY  1
#define FIM_RULE_ALLOW 2

/* 文件模式常量 */
#define FMODE_READ      0x1
#define FMODE_WRITE     0x2

/* 简化的规则条目结构 */
struct fim_rule {
    __u32 rule_type;       // 规则类型：允许/拒绝
    __u32 is_dir;          // 是否为目录
    __u32 enabled;         // 是否启用
};

/* 读操作规则map - deny_list */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u32);
    __type(value, struct fim_rule);
    __uint(max_entries, 1024);
    __uint(pinning, LIBBPF_PIN_BY_NAME);
} read_deny_map SEC(".maps");

/* 读操作规则map - allow_list */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u32);
    __type(value, struct fim_rule);
    __uint(max_entries, 1024);
    __uint(pinning, LIBBPF_PIN_BY_NAME);
} read_allow_map SEC(".maps");

/* 写操作规则map - deny_list */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u32);
    __type(value, struct fim_rule);
    __uint(max_entries, 1024);
    __uint(pinning, LIBBPF_PIN_BY_NAME);
} write_deny_map SEC(".maps");

/* 写操作规则map - allow_list */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u32);
    __type(value, struct fim_rule);
    __uint(max_entries, 1024);
    __uint(pinning, LIBBPF_PIN_BY_NAME);
} write_allow_map SEC(".maps");

#endif /* __FIM_EBPF_COMMON_H__ */