#ifndef __FIM_EBPF_COMMON_H__
#define __FIM_EBPF_COMMON_H__

#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_core_read.h>

/* 最大路径长度 */
#define FIM_MAX_PATH_LEN    4096
#define FIM_MAX_FILENAME    256
#define FIM_MAX_WHITELIST   10000

/* 文件操作类型 */
enum fim_operation {
    FIM_OP_OPEN_READ    = 1,
    FIM_OP_OPEN_WRITE   = 2,
    FIM_OP_CREATE       = 3,
    FIM_OP_DELETE       = 4,
    FIM_OP_RENAME       = 5,
    FIM_OP_CHMOD        = 6,
    FIM_OP_CHOWN        = 7,
    FIM_OP_TRUNCATE     = 8,
};

/* 决策类型 */
enum fim_decision {
    FIM_ALLOW = 0,
    FIM_DENY  = 1,
};

/* 策略动作 */
enum fim_policy_action {
    FIM_POLICY_ALLOW = 0,
    FIM_POLICY_DENY  = 1,
};

/* inode键值结构 */
struct fim_inode_key {
    __u64 ino;
    __u32 dev;
};

/* 策略条目 */
struct fim_policy_entry {
    __u32 operations;
    __u32 action;
    char description[64];
};

/* 事件日志结构 */
struct fim_event {
    __u64 timestamp;
    __u32 pid;
    __u32 uid;
    __u32 operation;
    __u32 decision;
    __u64 ino;
    __u32 dev;
    char path[FIM_MAX_PATH_LEN];
    char comm[16];
};

/* 统计信息结构 */
struct fim_stats {
    __u64 total_events;
    __u64 allowed_events;
    __u64 denied_events;
    __u64 policy_hits;
};

/* 配置结构 */
struct fim_config {
    __u32 enabled;
    __u32 default_action;
    __u32 log_level;
    __u32 max_events;
};

/* 文件模式常量 */
#define FMODE_READ      0x1
#define FMODE_WRITE     0x2

/* 错误码 */
#define EACCES          13

/* 日志级别 */
#define FIM_LOG_ERROR   1
#define FIM_LOG_WARN    2
#define FIM_LOG_INFO    3
#define FIM_LOG_DEBUG   4

/* eBPF Maps */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, FIM_MAX_WHITELIST);
    __type(key, struct fim_inode_key);
    __type(value, struct fim_policy_entry);
    __uint(pinning, LIBBPF_PIN_BY_NAME);
} policy_map SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 256 * 1024);
} events_map SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, struct fim_stats);
} stats_map SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, struct fim_config);
} config_map SEC(".maps");

/* 获取配置 */
static inline struct fim_config *get_config(void) {
    __u32 key = 0;
    return bpf_map_lookup_elem(&config_map, &key);
}

/* 获取设备ID */
static inline __u32 get_device_id(struct inode *inode) {
    return BPF_CORE_READ(inode, i_sb, s_dev);
}

/* 检查策略或父目录权限 */
static inline int check_policy_or_parents(struct dentry *dentry, __u32 operation) {
    struct dentry *cur = dentry;

    for (int i = 0; i < 6; i++) {
        if (!cur || !cur->d_inode) break;

        struct fim_inode_key key;
        key.ino = BPF_CORE_READ(cur, d_inode, i_ino);
        key.dev = get_device_id(BPF_CORE_READ(cur, d_inode));

        struct fim_policy_entry *entry = bpf_map_lookup_elem(&policy_map, &key);
        if (entry && (entry->operations & operation)) {
            return (entry->action == FIM_POLICY_ALLOW) ? FIM_ALLOW : FIM_DENY;
        }

        if (cur == cur->d_parent) break;
        cur = cur->d_parent;
    }
    return -1; /* 未找到匹配规则 */
}

/* 记录事件日志 */
static inline void log_event(__u32 operation, __u32 decision, struct inode *inode, const char *path) {
    struct fim_event *event;
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    __u64 uid_gid = bpf_get_current_uid_gid();

    event = bpf_ringbuf_reserve(&events_map, sizeof(*event), 0);
    if (!event)
        return;

    event->timestamp = bpf_ktime_get_ns();
    event->pid = pid_tgid >> 32;
    event->uid = uid_gid & 0xFFFFFFFF;
    event->operation = operation;
    event->decision = decision;

    /* 记录inode信息 */
    if (inode) {
        event->ino = BPF_CORE_READ(inode, i_ino);
        event->dev = get_device_id(inode);
    } else {
        event->ino = 0;
        event->dev = 0;
    }

    bpf_get_current_comm(event->comm, sizeof(event->comm));
    if (path) {
        bpf_probe_read_kernel_str(event->path, sizeof(event->path), path);
    } else {
        event->path[0] = '\0';
    }

    bpf_ringbuf_submit(event, 0);
}

/* 更新统计信息 */
static inline void update_stats(__u32 decision, int policy_hit) {
    __u32 key = 0;
    struct fim_stats *stats;

    stats = bpf_map_lookup_elem(&stats_map, &key);
    if (!stats)
        return;

    __sync_fetch_and_add(&stats->total_events, 1);

    if (decision == FIM_ALLOW) {
        __sync_fetch_and_add(&stats->allowed_events, 1);
    } else {
        __sync_fetch_and_add(&stats->denied_events, 1);
    }

    if (policy_hit) {
        __sync_fetch_and_add(&stats->policy_hits, 1);
    }
}

#endif /* __FIM_EBPF_COMMON_H__ */