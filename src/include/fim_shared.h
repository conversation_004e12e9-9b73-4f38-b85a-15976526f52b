#ifndef __FIM_SHARED_H__
#define __FIM_SHARED_H__

#include <linux/types.h>

/* 最大路径长度 */
#define FIM_MAX_PATH_LEN    4096
#define FIM_MAX_FILENAME    256
#define FIM_MAX_WHITELIST   10000

/* 文件操作类型 */
enum fim_operation {
    FIM_OP_OPEN_READ    = 1,
    FIM_OP_OPEN_WRITE   = 2,
    FIM_OP_CREATE       = 3,
    FIM_OP_DELETE       = 4,
    FIM_OP_RENAME       = 5,
    FIM_OP_CHMOD        = 6,
    FIM_OP_CHOWN        = 7,
    FIM_OP_TRUNCATE     = 8,
};

/* 访问决策 */
enum fim_decision {
    FIM_ALLOW   = 0,
    FIM_DENY    = 1,
};

/* inode键值结构 */
struct fim_inode_key {
    __u64 ino;
    __u32 dev;
};

/* 策略条目结构 - 基于inode精准匹配 */
struct fim_policy_entry {
    __u32 operations;   /* 操作位掩码 */
    __u32 action;       /* 策略动作：允许或拒绝 */
    char description[64]; /* 描述信息 */
};

/* 事件日志结构 */
struct fim_event {
    __u64 timestamp;
    __u32 pid;
    __u32 uid;
    __u32 operation;
    __u32 decision;
    __u64 ino;                  /* 访问的inode号 */
    __u32 dev;                  /* 设备号 */
    char path[FIM_MAX_PATH_LEN]; /* 文件路径 */
    char comm[16];              /* 进程名 */
};

/* 统计信息结构 */
struct fim_stats {
    __u64 total_events;
    __u64 allowed_events;
    __u64 denied_events;
    __u64 policy_hits;
};

/* 文件类型标志（仅用于显示） */
#define FIM_TYPE_FILE       0x01    /* 文件 */
#define FIM_TYPE_DIR        0x02    /* 目录 */

/* 操作权限位掩码 */
#define FIM_PERM_READ       0x01
#define FIM_PERM_WRITE      0x02
#define FIM_PERM_CREATE     0x04
#define FIM_PERM_DELETE     0x08
#define FIM_PERM_RENAME     0x10
#define FIM_PERM_CHMOD      0x20
#define FIM_PERM_CHOWN      0x40
#define FIM_PERM_ALL        0xFF

/* eBPF Map类型定义 */
#define FIM_MAP_WHITELIST   0
#define FIM_MAP_EVENTS      1
#define FIM_MAP_STATS       2
#define FIM_MAP_CONFIG      3

/* 配置参数 */
struct fim_config {
    __u32 enabled;              /* 是否启用 */
    __u32 log_level;            /* 日志级别 */
    __u32 default_action;       /* 默认动作：允许或拒绝 */
    __u32 max_events;           /* 最大事件缓冲区大小 */
};

/* 策略动作 */
#define FIM_POLICY_ALLOW    0   /* 允许规则 */
#define FIM_POLICY_DENY     1   /* 拒绝规则 */

/* 规则类型（兼容性） */
#define FIM_RULE_ALLOW      0   /* 允许规则 */
#define FIM_RULE_DENY       1   /* 拒绝规则 */

/* 日志级别 */
#define FIM_LOG_ERROR   1
#define FIM_LOG_WARN    2
#define FIM_LOG_INFO    3
#define FIM_LOG_DEBUG   4

/* 用户空间与内核空间通信的控制命令 */
enum fim_cmd {
    FIM_CMD_ADD_POLICY      = 1,
    FIM_CMD_DEL_POLICY      = 2,
    FIM_CMD_CLEAR_POLICY    = 3,
    FIM_CMD_GET_STATS       = 4,
    FIM_CMD_SET_CONFIG      = 5,
    FIM_CMD_GET_CONFIG      = 6,
};

/* 控制消息结构 */
struct fim_ctrl_msg {
    __u32 cmd;
    __u32 data_len;
    union {
        struct fim_policy_entry policy;
        struct fim_stats stats;
        struct fim_config config;
    };
};

#endif /* __FIM_SHARED_H__ */
