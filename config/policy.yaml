# FIM Policy Configuration
# 文件访问控制策略配置文件
#
# 此文件定义了基于YAML格式的文件访问控制策略
# 支持混合使用允许(allow)和拒绝(deny)规则

# 全局配置
global:
  # 默认动作：当没有匹配的规则时的行为
  default_action: deny  # allow 或 deny
  
  # 日志级别
  log_level: info  # error, warn, info, debug
  
  # 描述
  description: "FIM Policy Configuration for Production Server"

# 策略规则列表
rules:
  # === 系统关键文件保护 ===
  
  # 允许读取系统配置文件
  - path: /etc/passwd
    operations: read
    action: allow
    description: "Allow reading user database"
    
  - path: /etc/group
    operations: read
    action: allow
    description: "Allow reading group database"
    
  - path: /etc/hosts
    operations: read,write
    action: allow
    description: "Allow network configuration"
    
  - path: /etc/resolv.conf
    operations: read,write
    action: allow
    description: "Allow DNS configuration"
    
  # 禁止修改关键系统文件
  - path: /etc/shadow
    operations: write,delete
    action: deny
    description: "Protect password database"
    
  - path: /etc/sudoers
    operations: write,delete
    action: deny
    description: "Protect sudo configuration"
    
  # === 应用程序目录 ===
  
  # 允许读取应用程序
  - path: /usr/bin
    operations: read
    action: allow
    description: "Allow executing system binaries"
    
  - path: /usr/lib
    operations: read
    action: allow
    description: "Allow loading system libraries"
    
  # 禁止修改应用程序
  - path: /usr/bin
    operations: write,delete
    action: deny
    description: "Protect system binaries"
    
  - path: /usr/sbin
    operations: write,delete
    action: deny
    description: "Protect system admin binaries"
    
  # === Web服务器配置 ===
  
  # 允许Web服务器配置
  - path: /etc/nginx
    operations: read
    action: allow
    description: "Allow reading nginx configuration"
    
  - path: /etc/nginx/nginx.conf
    operations: read,write
    action: allow
    description: "Allow modifying main nginx config"
    
  # 允许Web内容目录
  - path: /var/www
    operations: read
    action: allow
    description: "Allow serving web content"
    
  - path: /var/www/html
    operations: read,write,create,delete
    action: allow
    description: "Allow managing web content"
    
  # === 日志和临时文件 ===
  
  # 允许日志目录完全访问
  - path: /var/log
    operations: all
    action: allow
    description: "Allow logging operations"
    
  - path: /var/log/nginx
    operations: all
    action: allow
    description: "Allow nginx logging"
    
  # 允许临时目录
  - path: /tmp
    operations: all
    action: allow
    description: "Allow temporary files"
    
  - path: /var/tmp
    operations: all
    action: allow
    description: "Allow variable temporary files"
    
  # === 数据库相关 ===
  
  # 允许数据库程序
  - path: /usr/bin/mysql
    operations: read
    action: allow
    description: "Allow mysql client"
    
  - path: /usr/bin/mysqld
    operations: read
    action: allow
    description: "Allow mysql server"
    
  # 允许数据库配置
  - path: /etc/mysql
    operations: read
    action: allow
    description: "Allow reading mysql configuration"
    
  # 允许数据库数据目录
  - path: /var/lib/mysql
    operations: all
    action: allow
    description: "Allow mysql data operations"
    
  # === SSH配置 ===
  
  # 允许读取SSH配置
  - path: /etc/ssh/ssh_config
    operations: read
    action: allow
    description: "Allow reading SSH client config"
    
  - path: /etc/ssh/sshd_config
    operations: read
    action: allow
    description: "Allow reading SSH server config"
    
  # 禁止修改SSH密钥
  - path: /etc/ssh/ssh_host_rsa_key
    operations: write,delete
    action: deny
    description: "Protect SSH host key"
    
  - path: /etc/ssh/ssh_host_ed25519_key
    operations: write,delete
    action: deny
    description: "Protect SSH host key"
    
  # === 敏感目录保护 ===
  
  # 禁止访问root目录
  - path: /root
    operations: all
    action: deny
    description: "Protect root home directory"
    
  # 禁止访问私钥目录
  - path: /etc/ssl/private
    operations: all
    action: deny
    description: "Protect SSL private keys"
    
  # 禁止修改启动配置
  - path: /boot
    operations: write,delete
    action: deny
    description: "Protect boot configuration"
    
  # === 特定应用保护 ===
  
  # 保护Docker配置
  - path: /etc/docker
    operations: write,delete
    action: deny
    description: "Protect Docker configuration"
    
  # 保护systemd配置
  - path: /etc/systemd/system
    operations: write,delete
    action: deny
    description: "Protect systemd services"
    
  # === 开发环境规则 ===
  
  # 允许开发目录
  - path: /home/<USER>
    operations: all
    action: allow
    description: "Allow developer workspace"
    
  # 允许项目目录
  - path: /opt/projects
    operations: all
    action: allow
    description: "Allow project files"
    
  # === 监控和审计 ===
  
  # 允许监控工具读取
  - path: /proc
    operations: read
    action: allow
    description: "Allow system monitoring"
    
  - path: /sys
    operations: read
    action: allow
    description: "Allow system information"
    
  # 禁止修改审计配置
  - path: /etc/audit
    operations: write,delete
    action: deny
    description: "Protect audit configuration"

# 策略元数据
metadata:
  version: "1.0"
  created: "2024-01-01"
  author: "Security Team"
  environment: "production"
  
  # 策略统计
  total_rules: 35
  allow_rules: 20
  deny_rules: 15
