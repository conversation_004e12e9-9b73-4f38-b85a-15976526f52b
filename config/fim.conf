# File Access Control Agent Configuration
# 文件访问控制代理配置文件

# 基本设置
[general]
# 是否启用文件访问控制 (0=禁用, 1=启用)
enabled=1

# FIM只使用YAML策略模式，支持混合allow/deny规则

# 日志级别 (1=ERROR, 2=WARN, 3=INFO, 4=DEBUG)
log_level=3

# 默认动作 (0=允许, 1=拒绝)
# 当没有匹配的策略规则时的行为
default_action=1

# 最大事件缓冲区大小
max_events=10000

# 策略文件路径（仅在策略模式下使用）
policy_file=/etc/fim/policy.yaml

# 日志设置
[logging]
# 日志文件路径
log_file=/var/log/fim.log

# 日志文件最大大小 (MB)
max_log_size=100

# 日志文件保留数量
log_rotate_count=5

# 是否启用syslog
enable_syslog=1

# syslog设施
syslog_facility=daemon

# 性能设置
[performance]
# 白名单最大条目数
max_whitelist_entries=10000

# 事件处理批次大小
event_batch_size=100

# 统计信息更新间隔 (秒)
stats_update_interval=60

# 监控设置
[monitoring]
# 是否启用实时监控
enable_realtime_monitor=1

# 监控事件类型 (位掩码)
# 1=READ, 2=WRITE, 4=CREATE, 8=DELETE, 16=RENAME, 32=CHMOD, 64=CHOWN
monitor_operations=255

# 是否记录允许的操作
log_allowed_operations=0

# 是否记录拒绝的操作
log_denied_operations=1

# 安全设置
[security]
# 是否启用路径规范化
enable_path_normalization=1

# 是否启用符号链接解析
resolve_symlinks=1

# 最大路径深度
max_path_depth=32

# 是否启用进程白名单
enable_process_whitelist=0

# 进程白名单文件
process_whitelist_file=/etc/fim/process_whitelist.conf

# 网络设置 (用于远程管理)
[network]
# 是否启用远程管理
enable_remote_management=0

# 管理端口
management_port=9999

# 绑定地址
bind_address=127.0.0.1

# 是否启用TLS
enable_tls=1

# TLS证书文件
tls_cert_file=/etc/fim/server.crt

# TLS私钥文件
tls_key_file=/etc/fim/server.key

# 高级设置
[advanced]
# eBPF程序路径
ebpf_program_path=/usr/lib/fim/fim_lsm.o

# 是否启用调试模式
debug_mode=0

# 是否启用性能分析
enable_profiling=0

# 性能分析输出文件
profiling_output=/var/log/fim_profile.log

# 内存限制 (MB)
memory_limit=256

# CPU限制 (百分比)
cpu_limit=50
