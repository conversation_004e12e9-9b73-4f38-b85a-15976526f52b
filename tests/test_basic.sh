#!/bin/bash

# File Access Control Agent - Basic Functionality Tests
# 文件访问控制代理 - 基础功能测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试配置
TEST_DIR="/tmp/fim_test_$$"
TEST_FILE="$TEST_DIR/test_file.txt"
TEST_LOG="/tmp/fim_test.log"
FIM_CTL="/usr/bin/fim-ctl"
FIM_LOG="/usr/bin/fim-log"

# 测试计数器
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# 辅助函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 检查函数
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This test script must be run as root"
        exit 1
    fi
}

check_binaries() {
    log_info "Checking if FIM binaries exist..."
    ((TESTS_TOTAL++))
    
    if [[ -x "$FIM_CTL" ]] && [[ -x "$FIM_LOG" ]]; then
        log_success "FIM binaries found"
    else
        log_error "FIM binaries not found. Please install FIM first."
        exit 1
    fi
}

# 设置测试环境
setup_test_env() {
    log_info "Setting up test environment..."
    
    # 创建测试目录
    mkdir -p "$TEST_DIR"
    echo "This is a test file" > "$TEST_FILE"
    
    # 停止现有的FIM服务（如果运行中）
    systemctl stop fim.service 2>/dev/null || true
    systemctl stop fim-log.service 2>/dev/null || true
    
    log_info "Test environment ready"
}

# 清理测试环境
cleanup_test_env() {
    log_info "Cleaning up test environment..."
    
    # 停止FIM进程
    pkill -f fim-ctl 2>/dev/null || true
    pkill -f fim-log 2>/dev/null || true
    
    # 删除测试文件
    rm -rf "$TEST_DIR" 2>/dev/null || true
    rm -f "$TEST_LOG" 2>/dev/null || true
    
    log_info "Test environment cleaned up"
}

# 测试1: 白名单管理
test_whitelist_management() {
    log_info "Testing whitelist management..."
    
    # 测试添加白名单条目
    ((TESTS_TOTAL++))
    if $FIM_CTL -c add -p "$TEST_FILE" -o "read,write" >/dev/null 2>&1; then
        log_success "Add whitelist entry"
    else
        log_error "Failed to add whitelist entry"
        return 1
    fi
    
    # 测试列出白名单条目
    ((TESTS_TOTAL++))
    if $FIM_CTL -c list | grep -q "$TEST_FILE"; then
        log_success "List whitelist entries"
    else
        log_error "Failed to list whitelist entries"
        return 1
    fi
    
    # 测试删除白名单条目
    ((TESTS_TOTAL++))
    if $FIM_CTL -c del -p "$TEST_FILE" >/dev/null 2>&1; then
        log_success "Delete whitelist entry"
    else
        log_error "Failed to delete whitelist entry"
        return 1
    fi
    
    # 验证条目已删除
    ((TESTS_TOTAL++))
    if ! $FIM_CTL -c list | grep -q "$TEST_FILE"; then
        log_success "Verify entry deletion"
    else
        log_error "Entry still exists after deletion"
        return 1
    fi
    
    return 0
}

# 测试2: 统计信息
test_statistics() {
    log_info "Testing statistics functionality..."
    
    # 测试获取统计信息
    ((TESTS_TOTAL++))
    if $FIM_CTL -c stats >/dev/null 2>&1; then
        log_success "Get statistics"
    else
        log_error "Failed to get statistics"
        return 1
    fi
    
    return 0
}

# 测试3: 配置加载
test_configuration() {
    log_info "Testing configuration loading..."
    
    # 创建临时配置文件
    local temp_config="/tmp/fim_test.conf"
    cat > "$temp_config" << EOF
# Test configuration
[general]
enabled=1
log_level=3
default_action=1
max_events=1000
EOF
    
    ((TESTS_TOTAL++))
    if [[ -f "$temp_config" ]]; then
        log_success "Create test configuration"
    else
        log_error "Failed to create test configuration"
        return 1
    fi
    
    # 清理
    rm -f "$temp_config"
    return 0
}

# 测试4: 守护进程模式
test_daemon_mode() {
    log_info "Testing daemon mode..."
    
    # 启动守护进程
    ((TESTS_TOTAL++))
    $FIM_CTL -c daemon &
    local daemon_pid=$!
    sleep 2
    
    if kill -0 $daemon_pid 2>/dev/null; then
        log_success "Start daemon mode"
        
        # 停止守护进程
        kill -TERM $daemon_pid 2>/dev/null || true
        wait $daemon_pid 2>/dev/null || true
    else
        log_error "Failed to start daemon mode"
        return 1
    fi
    
    return 0
}

# 测试5: 日志功能
test_logging() {
    log_info "Testing logging functionality..."
    
    # 测试日志程序启动
    ((TESTS_TOTAL++))
    timeout 5 $FIM_LOG -v &
    local log_pid=$!
    sleep 1
    
    if kill -0 $log_pid 2>/dev/null; then
        log_success "Start logging process"
        kill -TERM $log_pid 2>/dev/null || true
        wait $log_pid 2>/dev/null || true
    else
        log_error "Failed to start logging process"
        return 1
    fi
    
    return 0
}

# 测试6: 文件访问控制
test_file_access_control() {
    log_info "Testing file access control..."
    
    # 添加测试文件到白名单
    $FIM_CTL -c add -p "$TEST_FILE" -o "read" >/dev/null 2>&1
    
    # 启动FIM守护进程
    $FIM_CTL -c daemon &
    local daemon_pid=$!
    sleep 2
    
    # 测试文件访问（这个测试可能需要实际的eBPF程序运行）
    ((TESTS_TOTAL++))
    if [[ -f "$TEST_FILE" ]]; then
        # 尝试读取文件
        if cat "$TEST_FILE" >/dev/null 2>&1; then
            log_success "File read access (whitelisted)"
        else
            log_warning "File read access test inconclusive (eBPF may not be loaded)"
        fi
    else
        log_error "Test file not found"
    fi
    
    # 清理
    kill -TERM $daemon_pid 2>/dev/null || true
    wait $daemon_pid 2>/dev/null || true
    $FIM_CTL -c del -p "$TEST_FILE" >/dev/null 2>&1 || true
    
    return 0
}

# 测试7: 错误处理
test_error_handling() {
    log_info "Testing error handling..."
    
    # 测试无效命令
    ((TESTS_TOTAL++))
    if ! $FIM_CTL -c invalid_command >/dev/null 2>&1; then
        log_success "Handle invalid command"
    else
        log_error "Should fail on invalid command"
        return 1
    fi
    
    # 测试无效路径
    ((TESTS_TOTAL++))
    if ! $FIM_CTL -c add -p "/nonexistent/path/file" >/dev/null 2>&1; then
        log_success "Handle invalid path"
    else
        log_warning "Adding nonexistent path succeeded (may be expected behavior)"
    fi
    
    return 0
}

# 性能测试
test_performance() {
    log_info "Running basic performance tests..."
    
    local start_time=$(date +%s.%N)
    
    # 批量添加白名单条目
    for i in {1..100}; do
        $FIM_CTL -c add -p "/tmp/test_file_$i" -o "read" >/dev/null 2>&1
    done
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc -l)
    
    ((TESTS_TOTAL++))
    if (( $(echo "$duration < 10" | bc -l) )); then
        log_success "Performance test: Added 100 entries in ${duration}s"
    else
        log_warning "Performance test: Took ${duration}s to add 100 entries (may be slow)"
    fi
    
    # 清理
    $FIM_CTL -c clear >/dev/null 2>&1
    
    return 0
}

# 运行所有测试
run_all_tests() {
    log_info "Starting FIM basic functionality tests..."
    echo
    
    # 运行测试
    check_binaries
    test_whitelist_management
    test_statistics
    test_configuration
    test_daemon_mode
    test_logging
    test_file_access_control
    test_error_handling
    test_performance
    
    echo
    log_info "Test Summary:"
    echo "  Total tests: $TESTS_TOTAL"
    echo "  Passed: $TESTS_PASSED"
    echo "  Failed: $TESTS_FAILED"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}All tests passed!${NC}"
        return 0
    else
        echo -e "${RED}Some tests failed!${NC}"
        return 1
    fi
}

# 主函数
main() {
    # 信号处理
    trap cleanup_test_env EXIT
    
    check_root
    setup_test_env
    run_all_tests
    
    return $?
}

# 运行测试
main "$@"
