#!/bin/bash

# File Access Control Agent - Inode-based Functionality Tests
# 文件访问控制代理 - 基于inode功能测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试配置
TEST_DIR="/tmp/fim_inode_test_$$"
TEST_FILE="$TEST_DIR/test_file.txt"
TEST_LINK="$TEST_DIR/test_link.txt"
TEST_SUBDIR="$TEST_DIR/subdir"
FIM_CTL="/usr/bin/fim-ctl"
FIM_INODE_TOOL="/usr/bin/fim-inode-tool"

# 测试计数器
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# 辅助函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 检查函数
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This test script must be run as root"
        exit 1
    fi
}

check_binaries() {
    log_info "Checking if FIM binaries exist..."
    ((TESTS_TOTAL++))
    
    if [[ -x "$FIM_CTL" ]] && [[ -x "$FIM_INODE_TOOL" ]]; then
        log_success "FIM binaries found"
    else
        log_error "FIM binaries not found. Please install FIM first."
        exit 1
    fi
}

# 设置测试环境
setup_test_env() {
    log_info "Setting up inode-based test environment..."
    
    # 创建测试目录和文件
    mkdir -p "$TEST_DIR"
    mkdir -p "$TEST_SUBDIR"
    echo "This is a test file" > "$TEST_FILE"
    echo "This is a subdirectory file" > "$TEST_SUBDIR/sub_file.txt"
    
    # 创建硬链接
    ln "$TEST_FILE" "$TEST_LINK"
    
    # 停止现有的FIM服务
    systemctl stop fim.service 2>/dev/null || true
    systemctl stop fim-log.service 2>/dev/null || true
    
    log_info "Inode-based test environment ready"
}

# 清理测试环境
cleanup_test_env() {
    log_info "Cleaning up inode-based test environment..."
    
    # 停止FIM进程
    pkill -f fim-ctl 2>/dev/null || true
    pkill -f fim-log 2>/dev/null || true
    
    # 删除测试文件
    rm -rf "$TEST_DIR" 2>/dev/null || true
    
    # 清空白名单
    $FIM_CTL -c clear >/dev/null 2>&1 || true
    
    log_info "Inode-based test environment cleaned up"
}

# 测试1: inode信息显示
test_inode_info() {
    log_info "Testing inode information display..."
    
    ((TESTS_TOTAL++))
    if $FIM_INODE_TOOL info "$TEST_FILE" | grep -q "Inode:"; then
        log_success "Inode info display works"
    else
        log_error "Failed to display inode info"
        return 1
    fi
    
    return 0
}

# 测试2: 基于inode的白名单添加
test_inode_whitelist_add() {
    log_info "Testing inode-based whitelist addition..."

    # 测试添加文件
    ((TESTS_TOTAL++))
    if $FIM_CTL -c add -p "$TEST_FILE" -o "read,write" >/dev/null 2>&1; then
        log_success "Add file to inode whitelist"
    else
        log_error "Failed to add file to inode whitelist"
        return 1
    fi

    # 测试添加目录
    ((TESTS_TOTAL++))
    if $FIM_CTL -c add -p "$TEST_DIR" -o "all" >/dev/null 2>&1; then
        log_success "Add directory to inode whitelist"
    else
        log_error "Failed to add directory to inode whitelist"
        return 1
    fi

    return 0
}

# 测试3: 硬链接处理
test_hardlink_handling() {
    log_info "Testing hardlink handling..."
    
    # 获取原文件和硬链接的inode信息
    local orig_inode=$($FIM_INODE_TOOL info "$TEST_FILE" | grep "Inode:" | awk '{print $2}')
    local link_inode=$($FIM_INODE_TOOL info "$TEST_LINK" | grep "Inode:" | awk '{print $2}')
    
    ((TESTS_TOTAL++))
    if [[ "$orig_inode" == "$link_inode" ]]; then
        log_success "Hardlinks have same inode"
    else
        log_error "Hardlinks have different inodes"
        return 1
    fi
    
    # 测试硬链接查找
    ((TESTS_TOTAL++))
    if $FIM_INODE_TOOL find-links "$TEST_FILE" | grep -q "Links found: 2"; then
        log_success "Hardlink detection works"
    else
        log_error "Failed to detect hardlinks"
        return 1
    fi
    
    return 0
}

# 测试4: 目录树显示
test_directory_tree() {
    log_info "Testing directory tree display..."
    
    ((TESTS_TOTAL++))
    if $FIM_INODE_TOOL tree "$TEST_DIR" 2 | grep -q "sub_file.txt"; then
        log_success "Directory tree display works"
    else
        log_error "Failed to display directory tree"
        return 1
    fi
    
    return 0
}

# 测试5: inode比较
test_inode_comparison() {
    log_info "Testing inode comparison..."
    
    # 比较相同文件（硬链接）
    ((TESTS_TOTAL++))
    if $FIM_INODE_TOOL compare "$TEST_FILE" "$TEST_LINK" | grep -q "Same file"; then
        log_success "Inode comparison for hardlinks works"
    else
        log_error "Failed to compare hardlinks correctly"
        return 1
    fi
    
    # 比较不同文件
    ((TESTS_TOTAL++))
    if $FIM_INODE_TOOL compare "$TEST_FILE" "$TEST_SUBDIR/sub_file.txt" | grep -q "Different files"; then
        log_success "Inode comparison for different files works"
    else
        log_error "Failed to compare different files correctly"
        return 1
    fi
    
    return 0
}

# 测试6: 白名单列表显示（基于inode）
test_inode_whitelist_list() {
    log_info "Testing inode-based whitelist listing..."
    
    # 添加一些条目
    $FIM_CTL -c add -p "$TEST_FILE" -o "read" >/dev/null 2>&1
    $FIM_CTL -c add -p "$TEST_DIR" -o "all" >/dev/null 2>&1
    
    ((TESTS_TOTAL++))
    local list_output=$($FIM_CTL -c list)
    if echo "$list_output" | grep -q "Inode" && echo "$list_output" | grep -q "Device"; then
        log_success "Inode-based whitelist listing works"
    else
        log_error "Failed to list inode-based whitelist"
        return 1
    fi
    
    return 0
}

# 测试7: 基于inode的删除
test_inode_whitelist_delete() {
    log_info "Testing inode-based whitelist deletion..."
    
    # 先添加条目
    $FIM_CTL -c add -p "$TEST_FILE" -o "read" >/dev/null 2>&1
    
    # 然后删除
    ((TESTS_TOTAL++))
    if $FIM_CTL -c del -p "$TEST_FILE" >/dev/null 2>&1; then
        log_success "Delete from inode whitelist works"
    else
        log_error "Failed to delete from inode whitelist"
        return 1
    fi
    
    # 验证已删除
    ((TESTS_TOTAL++))
    if ! $FIM_CTL -c list | grep -q "$TEST_FILE"; then
        log_success "Verify inode whitelist deletion"
    else
        log_error "Entry still exists after deletion"
        return 1
    fi
    
    return 0
}

# 测试8: 双模式功能测试
test_dual_mode_functionality() {
    log_info "Testing dual mode functionality..."

    # 测试模式设置和查看
    ((TESTS_TOTAL++))
    if $FIM_CTL -c mode -m whitelist >/dev/null 2>&1; then
        log_success "Set whitelist mode"
    else
        log_error "Failed to set whitelist mode"
        return 1
    fi

    ((TESTS_TOTAL++))
    if $FIM_CTL -c mode | grep -q "whitelist"; then
        log_success "Query whitelist mode"
    else
        log_error "Failed to query whitelist mode"
        return 1
    fi

    ((TESTS_TOTAL++))
    if $FIM_CTL -c mode -m blacklist >/dev/null 2>&1; then
        log_success "Set blacklist mode"
    else
        log_error "Failed to set blacklist mode"
        return 1
    fi

    ((TESTS_TOTAL++))
    if $FIM_CTL -c mode | grep -q "blacklist"; then
        log_success "Query blacklist mode"
    else
        log_error "Failed to query blacklist mode"
        return 1
    fi

    # 重置为白名单模式
    $FIM_CTL -c mode -m whitelist >/dev/null 2>&1

    return 0
}

# 测试9: 文件类型检测
test_file_type_detection() {
    log_info "Testing file type detection..."
    
    # 测试文件类型检测
    ((TESTS_TOTAL++))
    if $FIM_INODE_TOOL info "$TEST_FILE" | grep -q "Regular file"; then
        log_success "File type detection works for files"
    else
        log_error "Failed to detect file type for regular file"
        return 1
    fi
    
    ((TESTS_TOTAL++))
    if $FIM_INODE_TOOL info "$TEST_DIR" | grep -q "Directory"; then
        log_success "File type detection works for directories"
    else
        log_error "Failed to detect file type for directory"
        return 1
    fi
    
    return 0
}

# 测试10: 性能比较（inode vs 路径）
test_performance_comparison() {
    log_info "Testing performance comparison (inode vs path)..."
    
    local start_time=$(date +%s.%N)
    
    # 批量添加基于inode的条目
    for i in {1..50}; do
        echo "test content $i" > "$TEST_DIR/perf_test_$i.txt"
        $FIM_CTL -c add -p "$TEST_DIR/perf_test_$i.txt" -o "read" >/dev/null 2>&1
    done
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc -l)
    
    ((TESTS_TOTAL++))
    if (( $(echo "$duration < 5" | bc -l) )); then
        log_success "Inode-based operations performance is good (${duration}s for 50 entries)"
    else
        log_warning "Inode-based operations may be slow (${duration}s for 50 entries)"
    fi
    
    # 清理测试文件
    rm -f "$TEST_DIR"/perf_test_*.txt
    $FIM_CTL -c clear >/dev/null 2>&1
    
    return 0
}

# 运行所有测试
run_all_tests() {
    log_info "Starting FIM inode-based functionality tests..."
    echo
    
    # 运行测试
    check_binaries
    test_inode_info
    test_inode_whitelist_add
    test_hardlink_handling
    test_directory_tree
    test_inode_comparison
    test_inode_whitelist_list
    test_inode_whitelist_delete
    test_dual_mode_functionality
    test_file_type_detection
    test_performance_comparison
    
    echo
    log_info "Inode-based Test Summary:"
    echo "  Total tests: $TESTS_TOTAL"
    echo "  Passed: $TESTS_PASSED"
    echo "  Failed: $TESTS_FAILED"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}All inode-based tests passed!${NC}"
        return 0
    else
        echo -e "${RED}Some inode-based tests failed!${NC}"
        return 1
    fi
}

# 主函数
main() {
    # 信号处理
    trap cleanup_test_env EXIT
    
    check_root
    setup_test_env
    run_all_tests
    
    return $?
}

# 运行测试
main "$@"
