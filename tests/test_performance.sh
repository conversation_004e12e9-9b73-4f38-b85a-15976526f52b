#!/bin/bash

# File Access Control Agent - Performance Tests
# 文件访问控制代理 - 性能测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试配置
TEST_DIR="/tmp/fim_perf_test_$$"
FIM_CTL="/usr/bin/fim-ctl"
RESULTS_FILE="/tmp/fim_performance_results.txt"

# 性能测试参数
WHITELIST_ENTRIES=1000
FILE_OPERATIONS=10000
CONCURRENT_PROCESSES=10

# 辅助函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 检查依赖
check_dependencies() {
    if ! command -v bc &> /dev/null; then
        log_error "bc calculator is required for performance tests"
        exit 1
    fi
    
    if [[ $EUID -ne 0 ]]; then
        log_error "This test script must be run as root"
        exit 1
    fi
    
    if [[ ! -x "$FIM_CTL" ]]; then
        log_error "FIM control binary not found: $FIM_CTL"
        exit 1
    fi
}

# 设置测试环境
setup_test_env() {
    log_info "Setting up performance test environment..."
    
    mkdir -p "$TEST_DIR"
    
    # 创建测试文件
    for i in $(seq 1 100); do
        echo "Test file content $i" > "$TEST_DIR/test_file_$i.txt"
    done
    
    # 停止现有服务
    systemctl stop fim.service 2>/dev/null || true
    systemctl stop fim-log.service 2>/dev/null || true
    
    # 清空现有白名单
    $FIM_CTL -c clear >/dev/null 2>&1 || true
    
    log_info "Performance test environment ready"
}

# 清理测试环境
cleanup_test_env() {
    log_info "Cleaning up performance test environment..."
    
    pkill -f fim-ctl 2>/dev/null || true
    pkill -f fim-log 2>/dev/null || true
    
    rm -rf "$TEST_DIR" 2>/dev/null || true
    
    # 清空白名单
    $FIM_CTL -c clear >/dev/null 2>&1 || true
    
    log_info "Performance test environment cleaned up"
}

# 测试1: 白名单添加性能
test_whitelist_add_performance() {
    log_info "Testing whitelist addition performance..."
    
    local start_time=$(date +%s.%N)
    
    for i in $(seq 1 $WHITELIST_ENTRIES); do
        $FIM_CTL -c add -p "$TEST_DIR/perf_test_$i.txt" -o "read,write" >/dev/null 2>&1
    done
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc -l)
    local ops_per_sec=$(echo "scale=2; $WHITELIST_ENTRIES / $duration" | bc -l)
    
    echo "Whitelist Addition Performance:" >> "$RESULTS_FILE"
    echo "  Entries: $WHITELIST_ENTRIES" >> "$RESULTS_FILE"
    echo "  Duration: ${duration}s" >> "$RESULTS_FILE"
    echo "  Operations/sec: $ops_per_sec" >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
    
    log_success "Added $WHITELIST_ENTRIES entries in ${duration}s ($ops_per_sec ops/sec)"
    
    # 性能基准检查
    if (( $(echo "$ops_per_sec > 100" | bc -l) )); then
        log_success "Whitelist addition performance is good"
    else
        log_warning "Whitelist addition performance may be slow"
    fi
}

# 测试2: 白名单查询性能
test_whitelist_lookup_performance() {
    log_info "Testing whitelist lookup performance..."
    
    # 首先添加一些条目
    for i in $(seq 1 100); do
        $FIM_CTL -c add -p "$TEST_DIR/lookup_test_$i.txt" -o "read" >/dev/null 2>&1
    done
    
    local start_time=$(date +%s.%N)
    
    # 执行查询操作（通过列出白名单）
    for i in $(seq 1 1000); do
        $FIM_CTL -c list >/dev/null 2>&1
    done
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc -l)
    local ops_per_sec=$(echo "scale=2; 1000 / $duration" | bc -l)
    
    echo "Whitelist Lookup Performance:" >> "$RESULTS_FILE"
    echo "  Lookups: 1000" >> "$RESULTS_FILE"
    echo "  Duration: ${duration}s" >> "$RESULTS_FILE"
    echo "  Operations/sec: $ops_per_sec" >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
    
    log_success "Performed 1000 lookups in ${duration}s ($ops_per_sec ops/sec)"
}

# 测试3: 内存使用测试
test_memory_usage() {
    log_info "Testing memory usage..."
    
    # 启动FIM守护进程
    $FIM_CTL -c daemon &
    local daemon_pid=$!
    sleep 2
    
    # 获取初始内存使用
    local initial_memory=$(ps -o rss= -p $daemon_pid 2>/dev/null || echo "0")
    
    # 添加大量白名单条目
    for i in $(seq 1 5000); do
        $FIM_CTL -c add -p "$TEST_DIR/memory_test_$i.txt" -o "all" >/dev/null 2>&1
    done
    
    sleep 1
    
    # 获取最终内存使用
    local final_memory=$(ps -o rss= -p $daemon_pid 2>/dev/null || echo "0")
    local memory_diff=$((final_memory - initial_memory))
    
    echo "Memory Usage Test:" >> "$RESULTS_FILE"
    echo "  Initial memory: ${initial_memory} KB" >> "$RESULTS_FILE"
    echo "  Final memory: ${final_memory} KB" >> "$RESULTS_FILE"
    echo "  Memory increase: ${memory_diff} KB" >> "$RESULTS_FILE"
    echo "  Entries added: 5000" >> "$RESULTS_FILE"
    echo "  Memory per entry: $(echo "scale=2; $memory_diff / 5000" | bc -l) KB" >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
    
    log_success "Memory usage: ${memory_diff} KB for 5000 entries"
    
    # 清理
    kill -TERM $daemon_pid 2>/dev/null || true
    wait $daemon_pid 2>/dev/null || true
    
    # 内存使用基准检查
    if [[ $memory_diff -lt 100000 ]]; then  # 小于100MB
        log_success "Memory usage is reasonable"
    else
        log_warning "Memory usage may be high"
    fi
}

# 测试4: 并发性能测试
test_concurrent_performance() {
    log_info "Testing concurrent performance..."
    
    # 启动FIM守护进程
    $FIM_CTL -c daemon &
    local daemon_pid=$!
    sleep 2
    
    local start_time=$(date +%s.%N)
    
    # 启动多个并发进程
    for i in $(seq 1 $CONCURRENT_PROCESSES); do
        (
            for j in $(seq 1 100); do
                $FIM_CTL -c add -p "$TEST_DIR/concurrent_${i}_${j}.txt" -o "read" >/dev/null 2>&1
            done
        ) &
    done
    
    # 等待所有进程完成
    wait
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc -l)
    local total_ops=$((CONCURRENT_PROCESSES * 100))
    local ops_per_sec=$(echo "scale=2; $total_ops / $duration" | bc -l)
    
    echo "Concurrent Performance Test:" >> "$RESULTS_FILE"
    echo "  Concurrent processes: $CONCURRENT_PROCESSES" >> "$RESULTS_FILE"
    echo "  Total operations: $total_ops" >> "$RESULTS_FILE"
    echo "  Duration: ${duration}s" >> "$RESULTS_FILE"
    echo "  Operations/sec: $ops_per_sec" >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
    
    log_success "Concurrent test: $total_ops ops in ${duration}s ($ops_per_sec ops/sec)"
    
    # 清理
    kill -TERM $daemon_pid 2>/dev/null || true
    wait $daemon_pid 2>/dev/null || true
}

# 测试5: 文件操作延迟测试
test_file_operation_latency() {
    log_info "Testing file operation latency..."
    
    # 添加测试文件到白名单
    for i in $(seq 1 10); do
        $FIM_CTL -c add -p "$TEST_DIR/test_file_$i.txt" -o "all" >/dev/null 2>&1
    done
    
    # 启动FIM守护进程
    $FIM_CTL -c daemon &
    local daemon_pid=$!
    sleep 2
    
    local total_time=0
    local operations=1000
    
    # 测试文件读取延迟
    for i in $(seq 1 $operations); do
        local file_index=$((i % 10 + 1))
        local start_time=$(date +%s.%N)
        cat "$TEST_DIR/test_file_$file_index.txt" >/dev/null 2>&1
        local end_time=$(date +%s.%N)
        local op_time=$(echo "$end_time - $start_time" | bc -l)
        total_time=$(echo "$total_time + $op_time" | bc -l)
    done
    
    local avg_latency=$(echo "scale=6; $total_time / $operations * 1000" | bc -l)
    
    echo "File Operation Latency Test:" >> "$RESULTS_FILE"
    echo "  Operations: $operations" >> "$RESULTS_FILE"
    echo "  Total time: ${total_time}s" >> "$RESULTS_FILE"
    echo "  Average latency: ${avg_latency}ms" >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
    
    log_success "Average file operation latency: ${avg_latency}ms"
    
    # 清理
    kill -TERM $daemon_pid 2>/dev/null || true
    wait $daemon_pid 2>/dev/null || true
    
    # 延迟基准检查
    if (( $(echo "$avg_latency < 1" | bc -l) )); then
        log_success "File operation latency is excellent"
    elif (( $(echo "$avg_latency < 10" | bc -l) )); then
        log_success "File operation latency is good"
    else
        log_warning "File operation latency may be high"
    fi
}

# 测试6: 系统资源监控
test_system_resources() {
    log_info "Testing system resource usage..."
    
    # 启动FIM守护进程
    $FIM_CTL -c daemon &
    local daemon_pid=$!
    sleep 2
    
    # 监控资源使用
    local cpu_usage=$(ps -o %cpu= -p $daemon_pid 2>/dev/null || echo "0")
    local memory_usage=$(ps -o rss= -p $daemon_pid 2>/dev/null || echo "0")
    local fd_count=$(ls -1 /proc/$daemon_pid/fd 2>/dev/null | wc -l || echo "0")
    
    # 执行一些操作来增加负载
    for i in $(seq 1 1000); do
        $FIM_CTL -c add -p "$TEST_DIR/resource_test_$i.txt" -o "read" >/dev/null 2>&1
    done
    
    sleep 2
    
    local cpu_usage_after=$(ps -o %cpu= -p $daemon_pid 2>/dev/null || echo "0")
    local memory_usage_after=$(ps -o rss= -p $daemon_pid 2>/dev/null || echo "0")
    local fd_count_after=$(ls -1 /proc/$daemon_pid/fd 2>/dev/null | wc -l || echo "0")
    
    echo "System Resource Usage:" >> "$RESULTS_FILE"
    echo "  Initial CPU usage: ${cpu_usage}%" >> "$RESULTS_FILE"
    echo "  Final CPU usage: ${cpu_usage_after}%" >> "$RESULTS_FILE"
    echo "  Initial memory: ${memory_usage} KB" >> "$RESULTS_FILE"
    echo "  Final memory: ${memory_usage_after} KB" >> "$RESULTS_FILE"
    echo "  Initial file descriptors: $fd_count" >> "$RESULTS_FILE"
    echo "  Final file descriptors: $fd_count_after" >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
    
    log_success "Resource monitoring completed"
    
    # 清理
    kill -TERM $daemon_pid 2>/dev/null || true
    wait $daemon_pid 2>/dev/null || true
}

# 生成性能报告
generate_performance_report() {
    log_info "Generating performance report..."
    
    echo "=== FIM Performance Test Report ===" > "$RESULTS_FILE"
    echo "Date: $(date)" >> "$RESULTS_FILE"
    echo "System: $(uname -a)" >> "$RESULTS_FILE"
    echo "CPU: $(grep 'model name' /proc/cpuinfo | head -1 | cut -d: -f2 | xargs)" >> "$RESULTS_FILE"
    echo "Memory: $(free -h | grep Mem | awk '{print $2}')" >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
}

# 运行所有性能测试
run_performance_tests() {
    log_info "Starting FIM performance tests..."
    echo
    
    generate_performance_report
    
    test_whitelist_add_performance
    test_whitelist_lookup_performance
    test_memory_usage
    test_concurrent_performance
    test_file_operation_latency
    test_system_resources
    
    echo
    log_info "Performance test results saved to: $RESULTS_FILE"
    log_success "All performance tests completed!"
    
    # 显示结果摘要
    echo
    log_info "Performance Test Summary:"
    cat "$RESULTS_FILE"
}

# 主函数
main() {
    trap cleanup_test_env EXIT
    
    check_dependencies
    setup_test_env
    run_performance_tests
    
    return 0
}

# 运行性能测试
main "$@"
